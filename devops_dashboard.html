<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>DevOps 总览 - 数智化运营平台</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <i class="fas fa-chart-line"></i> 数智化运营平台
      </div>
      <div style="display: flex; align-items: center;">
        <div class="dropdown" style="margin-right: 16px;">
          <button class="dropdown-toggle">
            <i class="fas fa-bell"></i>
            <span class="badge">3</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item">
              <div style="font-weight: 500;">新任务通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">您有3个新任务需要处理</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">数据采集完成</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">昨日数据采集已完成</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">系统更新</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">平台将于今晚23:00进行维护</div>
            </div>
          </div>
        </div>
        <div class="dropdown">
          <button class="dropdown-toggle">
            <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px;">
            <span>管理员</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item"><i class="fas fa-user"></i> 个人中心</div>
            <div class="dropdown-item"><i class="fas fa-cog"></i> 系统设置</div>
            <div class="dropdown-item"><i class="fas fa-sign-out-alt"></i> 退出登录</div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- 侧边栏 -->
  <div class="sidebar">
    <div class="menu-item" onclick="navigateToPage('index.html')">
      <i class="fas fa-home menu-icon"></i>
      <span class="menu-text">首页</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-database menu-icon"></i>
      <span class="menu-text">数据融通</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-pie menu-icon"></i>
      <span class="menu-text">智能洞察分析</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-bar menu-icon"></i>
      <span class="menu-text">运营视图</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tachometer-alt menu-icon"></i>
      <span class="menu-text">统一运营门户</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tasks menu-icon"></i>
      <span class="menu-text">五级穿透调度</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-server menu-icon"></i>
      <span class="menu-text">微服务管理</span>
    </div>
    
    <!-- DevOps 模块 -->
    <div class="menu-section">
      <div class="menu-section-title">DevOps 平台</div>
    </div>
    <div class="menu-item active" onclick="navigateToPage('devops_dashboard.html')">
      <i class="fas fa-tachometer-alt menu-icon"></i>
      <span class="menu-text">DevOps 总览</span>
    </div>
    <div class="menu-item" onclick="navigateToPage('pipeline_management.html')">
      <i class="fas fa-code-branch menu-icon"></i>
      <span class="menu-text">CI/CD 流水线</span>
    </div>
    <div class="menu-item" onclick="navigateToPage('deployment_management.html')">
      <i class="fas fa-cube menu-icon"></i>
      <span class="menu-text">容器部署</span>
    </div>
    <div class="menu-item" onclick="navigateToPage('monitoring_center.html')">
      <i class="fas fa-chart-line menu-icon"></i>
      <span class="menu-text">监控中心</span>
    </div>
    <div class="menu-item" onclick="navigateToPage('service_topology.html')">
      <i class="fas fa-project-diagram menu-icon"></i>
      <span class="menu-text">服务拓扑</span>
    </div>
    
    <div class="menu-item">
      <i class="fas fa-user-shield menu-icon"></i>
      <span class="menu-text">权限管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-cog menu-icon"></i>
      <span class="menu-text">系统设置</span>
    </div>
  </div>

  <!-- 主内容区 -->
  <div class="main-content">
    <div class="page-title">
      <i class="fas fa-tachometer-alt page-title-icon"></i>
      DevOps 系统总览
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit;">首页</a></div>
      <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit;">DevOps 平台</a></div>
      <div class="breadcrumb-item active">系统总览</div>
    </div>

    <!-- 页面标题和更新时间 -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px;">
      <div>
        <h1 style="font-size: 24px; font-weight: bold; margin: 0; color: var(--text-primary);">系统总览</h1>
        <p style="color: var(--text-secondary); margin: 4px 0 0 0;">DevOps 平台运行状态监控</p>
      </div>
      <div style="font-size: 14px; color: var(--text-tertiary);">
        最后更新: <span id="lastUpdateTime"></span>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row">
      <div class="col col-3">
        <div class="stat-card">
          <div class="stat-icon" style="background-color: rgba(24, 144, 255, 0.1); color: var(--primary-color);">
            <i class="fas fa-cube"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">24</div>
            <div class="stat-label">运行中的应用</div>
            <div class="stat-change" style="color: var(--success-color); font-size: 12px; margin-top: 4px;">
              <i class="fas fa-arrow-up"></i> +2 vs 昨日
            </div>
          </div>
        </div>
      </div>
      <div class="col col-3">
        <div class="stat-card">
          <div class="stat-icon" style="background-color: rgba(82, 196, 26, 0.1); color: var(--success-color);">
            <i class="fas fa-code-branch"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">18</div>
            <div class="stat-label">今日部署次数</div>
            <div class="stat-change" style="color: var(--success-color); font-size: 12px; margin-top: 4px;">
              <i class="fas fa-arrow-up"></i> +5 vs 昨日
            </div>
          </div>
        </div>
      </div>
      <div class="col col-3">
        <div class="stat-card">
          <div class="stat-icon" style="background-color: rgba(250, 173, 20, 0.1); color: var(--warning-color);">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">3</div>
            <div class="stat-label">活跃告警</div>
            <div class="stat-change" style="color: var(--success-color); font-size: 12px; margin-top: 4px;">
              <i class="fas fa-arrow-down"></i> -2 vs 昨日
            </div>
          </div>
        </div>
      </div>
      <div class="col col-3">
        <div class="stat-card">
          <div class="stat-icon" style="background-color: rgba(82, 196, 26, 0.1); color: var(--success-color);">
            <i class="fas fa-chart-line"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">98.5%</div>
            <div class="stat-label">系统健康度</div>
            <div class="stat-change" style="color: var(--success-color); font-size: 12px; margin-top: 4px;">
              <i class="fas fa-arrow-up"></i> +0.2% vs 昨日
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="row">
      <div class="col col-6">
        <div class="card">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <h3 style="font-size: 18px; font-weight: 600; display: flex; align-items: center;">
              <i class="fas fa-arrow-up" style="margin-right: 8px; color: var(--text-tertiary);"></i>
              部署趋势
            </h3>
            <div class="dropdown">
              <button class="dropdown-toggle"><i class="fas fa-ellipsis-v"></i></button>
              <div class="dropdown-menu">
                <div class="dropdown-item">导出数据</div>
                <div class="dropdown-item">刷新</div>
                <div class="dropdown-item">设置</div>
              </div>
            </div>
          </div>
          <div class="chart-container">
            <canvas id="deploymentTrendChart"></canvas>
          </div>
        </div>
      </div>
      <div class="col col-6">
        <div class="card">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <h3 style="font-size: 18px; font-weight: 600; display: flex; align-items: center;">
              <i class="fas fa-chart-line" style="margin-right: 8px; color: var(--text-tertiary);"></i>
              集群资源使用率
            </h3>
            <div class="dropdown">
              <button class="dropdown-toggle"><i class="fas fa-ellipsis-v"></i></button>
              <div class="dropdown-menu">
                <div class="dropdown-item">导出数据</div>
                <div class="dropdown-item">刷新</div>
                <div class="dropdown-item">设置</div>
              </div>
            </div>
          </div>
          <div class="chart-container">
            <canvas id="resourceUsageChart"></canvas>
          </div>
          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-top: 16px;">
            <div style="display: flex; align-items: center; gap: 8px;">
              <div style="width: 12px; height: 12px; border-radius: 50%; background-color: #3B82F6;"></div>
              <span style="font-size: 14px; color: var(--text-secondary);">CPU</span>
              <span style="font-size: 14px; font-weight: 500; color: var(--text-primary);">65%</span>
            </div>
            <div style="display: flex; align-items: center; gap: 8px;">
              <div style="width: 12px; height: 12px; border-radius: 50%; background-color: #10B981;"></div>
              <span style="font-size: 14px; color: var(--text-secondary);">内存</span>
              <span style="font-size: 14px; font-weight: 500; color: var(--text-primary);">78%</span>
            </div>
            <div style="display: flex; align-items: center; gap: 8px;">
              <div style="width: 12px; height: 12px; border-radius: 50%; background-color: #F59E0B;"></div>
              <span style="font-size: 14px; color: var(--text-secondary);">存储</span>
              <span style="font-size: 14px; font-weight: 500; color: var(--text-primary);">45%</span>
            </div>
            <div style="display: flex; align-items: center; gap: 8px;">
              <div style="width: 12px; height: 12px; border-radius: 50%; background-color: #EF4444;"></div>
              <span style="font-size: 14px; color: var(--text-secondary);">网络</span>
              <span style="font-size: 14px; font-weight: 500; color: var(--text-primary);">32%</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近的流水线 -->
    <div class="card">
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; padding-bottom: 16px; border-bottom: 1px solid var(--border-color);">
        <h3 style="font-size: 18px; font-weight: 600;">最近的流水线</h3>
        <button class="btn btn-primary" onclick="navigateToPage('pipeline_management.html')">
          <i class="fas fa-eye"></i> 查看全部
        </button>
      </div>
      <div style="border-top: 1px solid var(--border-color);">
        <div id="pipelinesList">
          <!-- 流水线列表将通过JavaScript动态生成 -->
        </div>
      </div>
    </div>
  </div>

  <script src="js/common.js"></script>
  <script>
    // 模拟数据
    const deploymentTrendData = [
      { time: '00:00', deployments: 2, success: 2 },
      { time: '04:00', deployments: 1, success: 1 },
      { time: '08:00', deployments: 5, success: 4 },
      { time: '12:00', deployments: 8, success: 7 },
      { time: '16:00', deployments: 12, success: 11 },
      { time: '20:00', deployments: 6, success: 6 },
    ];

    const resourceUsageData = [
      { name: 'CPU', value: 65, color: '#3B82F6' },
      { name: '内存', value: 78, color: '#10B981' },
      { name: '存储', value: 45, color: '#F59E0B' },
      { name: '网络', value: 32, color: '#EF4444' },
    ];

    const recentPipelines = [
      {
        id: '1',
        name: 'user-service',
        status: 'success',
        duration: '2m 34s',
        time: '5分钟前',
        branch: 'main',
      },
      {
        id: '2',
        name: 'order-service',
        status: 'running',
        duration: '1m 45s',
        time: '正在进行',
        branch: 'develop',
      },
      {
        id: '3',
        name: 'payment-service',
        status: 'failed',
        duration: '3m 12s',
        time: '15分钟前',
        branch: 'main',
      },
      {
        id: '4',
        name: 'notification-service',
        status: 'success',
        duration: '1m 58s',
        time: '30分钟前',
        branch: 'main',
      },
    ];

    // 获取状态图标和颜色
    function getStatusIcon(status) {
      switch (status) {
        case 'success': return '<i class="fas fa-check-circle" style="color: var(--success-color);"></i>';
        case 'failed': return '<i class="fas fa-times-circle" style="color: var(--danger-color);"></i>';
        case 'running': return '<div style="width: 16px; height: 16px; border: 2px solid var(--primary-color); border-top: 2px solid transparent; border-radius: 50%; animation: spin 1s linear infinite;"></div>';
        default: return '<i class="fas fa-clock" style="color: var(--warning-color);"></i>';
      }
    }

    function getStatusText(status) {
      switch (status) {
        case 'success': return '成功';
        case 'failed': return '失败';
        case 'running': return '运行中';
        default: return '等待中';
      }
    }

    // 渲染流水线列表
    function renderPipelinesList() {
      const container = document.getElementById('pipelinesList');
      container.innerHTML = recentPipelines.map(pipeline => `
        <div style="display: flex; align-items: center; justify-content: space-between; padding: 16px 0; border-bottom: 1px solid var(--border-color);">
          <div style="display: flex; align-items: center; gap: 16px;">
            <div style="flex-shrink: 0;">
              ${getStatusIcon(pipeline.status)}
            </div>
            <div>
              <div style="font-size: 14px; font-weight: 500; color: var(--text-primary);">
                ${pipeline.name}
              </div>
              <div style="font-size: 14px; color: var(--text-secondary);">
                分支: ${pipeline.branch}
              </div>
            </div>
          </div>
          <div style="display: flex; align-items: center; gap: 16px; font-size: 14px; color: var(--text-secondary);">
            <div style="display: flex; align-items: center; gap: 4px;">
              <i class="fas fa-clock" style="width: 16px; height: 16px;"></i>
              <span>${pipeline.duration}</span>
            </div>
            <span>${pipeline.time}</span>
          </div>
        </div>
      `).join('');
    }

    // 初始化图表
    document.addEventListener('DOMContentLoaded', function() {
      // 更新时间
      document.getElementById('lastUpdateTime').textContent = new Date().toLocaleString('zh-CN');

      // 部署趋势图表
      const deploymentCtx = document.getElementById('deploymentTrendChart').getContext('2d');
      new Chart(deploymentCtx, {
        type: 'line',
        data: {
          labels: deploymentTrendData.map(item => item.time),
          datasets: [{
            label: '总部署次数',
            data: deploymentTrendData.map(item => item.deployments),
            borderColor: '#3B82F6',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            tension: 0.3,
            fill: true
          }, {
            label: '成功部署',
            data: deploymentTrendData.map(item => item.success),
            borderColor: '#10B981',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            tension: 0.3,
            fill: true
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'top',
            },
            tooltip: {
              mode: 'index',
              intersect: false,
            }
          },
          scales: {
            y: {
              beginAtZero: true
            }
          }
        }
      });

      // 资源使用情况图表
      const resourceCtx = document.getElementById('resourceUsageChart').getContext('2d');
      new Chart(resourceCtx, {
        type: 'doughnut',
        data: {
          labels: resourceUsageData.map(item => item.name),
          datasets: [{
            data: resourceUsageData.map(item => item.value),
            backgroundColor: resourceUsageData.map(item => item.color),
            borderWidth: 0
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            },
            tooltip: {
              callbacks: {
                label: function(context) {
                  return context.label + ': ' + context.parsed + '%';
                }
              }
            }
          }
        }
      });

      // 渲染流水线列表
      renderPipelinesList();
    });
  </script>

  <style>
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</body>
</html>
