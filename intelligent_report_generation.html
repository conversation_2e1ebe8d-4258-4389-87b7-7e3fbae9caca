<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>数智化运营平台 - 智能生成报表</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <i class="fas fa-chart-line"></i> 数智化运营平台
      </div>
      <div style="display: flex; align-items: center;">
        <div class="dropdown" style="margin-right: 16px;">
          <button class="dropdown-toggle">
            <i class="fas fa-bell"></i>
            <span class="badge">3</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item">
              <div style="font-weight: 500;">新任务通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">您有3个新任务需要处理</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">数据采集完成</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">昨日数据采集已完成</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">系统更新</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">平台将于今晚23:00进行维护</div>
            </div>
          </div>
        </div>
        <div class="dropdown">
          <button class="dropdown-toggle">
            <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px;">
            <span>管理员</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item"><i class="fas fa-user"></i> 个人中心</div>
            <div class="dropdown-item"><i class="fas fa-cog"></i> 系统设置</div>
            <div class="dropdown-item"><i class="fas fa-sign-out-alt"></i> 退出登录</div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- 侧边栏 -->
  <div class="sidebar">
    <div class="menu-item">
      <i class="fas fa-home menu-icon"></i>
      <span class="menu-text">首页</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-database menu-icon"></i>
      <span class="menu-text">数据融通</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-pie menu-icon"></i>
      <span class="menu-text">智能洞察分析</span>
    </div>
    <div class="menu-item active">
      <i class="fas fa-chart-bar menu-icon"></i>
      <span class="menu-text">运营视图</span>
      <div style="margin-left: 24px; margin-top: 8px; font-size: 14px;">
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">预置大屏模板</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">预制组件</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">模板管理</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">画布管理</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">数据源设置</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">自定义报表</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">报表管理</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">报表设计</div>
        <div style="padding: 8px 0; color: var(--primary-color); cursor: pointer;">智能生成报表</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">视图操作能力</div>
      </div>
    </div>
    <div class="menu-item">
      <i class="fas fa-tachometer-alt menu-icon"></i>
      <span class="menu-text">统一运营门户</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tasks menu-icon"></i>
      <span class="menu-text">五级穿透调度</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-server menu-icon"></i>
      <span class="menu-text">微服务管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-user-shield menu-icon"></i>
      <span class="menu-text">权限管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-cog menu-icon"></i>
      <span class="menu-text">系统设置</span>
    </div>
  </div>

  <!-- 主内容区 -->
  <div class="main-content">
    <div class="page-title">
      <i class="fas fa-robot page-title-icon"></i>
      智能生成报表
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit;">首页</a></div>
      <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit;">运营视图</a></div>
      <div class="breadcrumb-item active">智能生成报表</div>
    </div>

    <!-- 智能生成报表配置区域 -->
    <div class="card">
      <div class="card-header">
        报表生成配置
      </div>
      <div class="card-body">
        <div style="grid-template-columns: 1fr 1fr; gap: 20px; display: grid;">
          <div style="display: flex; flex-direction: column;">
            <label style="margin-bottom: 8px;">报表名称</label>
            <input type="text" placeholder="请输入报表名称" style="padding: 8px; border: 1px solid var(--border-color); border-radius: 4px; margin-bottom: 16px;">

            <label style="margin-bottom: 8px;">报表类型</label>
            <select style="padding: 8px; border: 1px solid var(--border-color); border-radius: 4px; margin-bottom: 16px;">
              <option value="daily">日报</option>
              <option value="weekly">周报</option>
              <option value="monthly">月报</option>
              <option value="quarterly">季报</option>
              <option value="custom">自定义</option>
            </select>

            <label style="margin-bottom: 8px;">数据来源</label>
            <select style="padding: 8px; border: 1px solid var(--border-color); border-radius: 4px; margin-bottom: 16px;">
              <option value="user_db">用户数据库</option>
              <option value="sales_db">销售数据库</option>
              <option value="log_file">日志文件</option>
              <option value="api">第三方API</option>
            </select>

            <label style="margin-bottom: 8px;">时间范围</label>
            <div style="display: flex; gap: 10px;">
              <input type="date" style="padding: 8px; border: 1px solid var(--border-color); border-radius: 4px;">
              <span style="margin: auto 0;">至</span>
              <input type="date" style="padding: 8px; border: 1px solid var(--border-color); border-radius: 4px;">
            </div>
          </div>

          <div style="display: flex; flex-direction: column;">
            <label style="margin-bottom: 8px;">报表主题</label>
            <select style="padding: 8px; border: 1px solid var(--border-color); border-radius: 4px; margin-bottom: 16px;">
              <option value="sales">销售分析</option>
              <option value="user">用户分析</option>
              <option value="operation">运营分析</option>
              <option value="performance">性能分析</option>
              <option value="custom">自定义</option>
            </select>

            <label style="margin-bottom: 8px;">智能分析维度</label>
            <div style="display: flex; flex-wrap: wrap; gap: 10px; margin-bottom: 16px;">
              <label style="display: flex; align-items: center;">
                <input type="checkbox" style="margin-right: 6px;"> 趋势分析
              </label>
              <label style="display: flex; align-items: center;">
                <input type="checkbox" style="margin-right: 6px;"> 同比环比
              </label>
              <label style="display: flex; align-items: center;">
                <input type="checkbox" style="margin-right: 6px;"> 异常检测
              </label>
              <label style="display: flex; align-items: center;">
                <input type="checkbox" style="margin-right: 6px;"> 预测分析
              </label>
              <label style="display: flex; align-items: center;">
                <input type="checkbox" style="margin-right: 6px;"> 相关性分析
              </label>
            </div>

            <label style="margin-bottom: 8px;">生成格式</label>
            <div style="display: flex; gap: 10px; margin-bottom: 16px;">
              <label style="display: flex; align-items: center;">
                <input type="radio" name="format" style="margin-right: 6px;" checked> HTML
              </label>
              <label style="display: flex; align-items: center;">
                <input type="radio" name="format" style="margin-right: 6px;"> PDF
              </label>
              <label style="display: flex; align-items: center;">
                <input type="radio" name="format" style="margin-right: 6px;"> Excel
              </label>
              <label style="display: flex; align-items: center;">
                <input type="radio" name="format" style="margin-right: 6px;"> Word
              </label>
            </div>

            <label style="margin-bottom: 8px;">高级选项</label>
            <button class="btn btn-secondary" style="margin-top: 8px; width: fit-content;"><i class="fas fa-cog"></i> 展开高级选项</button>
          </div>
        </div>

        <div style="margin-top: 20px; display: flex; justify-content: center;">
          <button class="btn btn-primary" style="margin-right: 10px;"><i class="fas fa-magic"></i> 智能生成报表</button>
          <button class="btn btn-secondary"><i class="fas fa-save"></i> 保存配置</button>
        </div>
      </div>
    </div>

    <!-- 历史生成记录 -->
    <div class="card" style="margin-top: 20px;">
      <div class="card-header">
        历史生成记录
      </div>
      <div class="card-body">
        <div class="table-container">
          <table class="table">
            <thead>
              <tr>
                <th>报表名称</th>
                <th>类型</th>
                <th>生成时间</th>
                <th>状态</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>销售日报_20230715</td>
                <td><span class="tag tag-info">日报</span></td>
                <td>2023-07-15 09:00</td>
                <td><span class="tag tag-success">已完成</span></td>
                <td>
                  <button class="btn" style="color: var(--primary-color);"><i class="fas fa-eye"></i></button>
                  <button class="btn" style="color: var(--primary-color);"><i class="fas fa-download"></i></button>
                  <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
                </td>
              </tr>
              <tr>
                <td>用户分析周报_20230710-20230716</td>
                <td><span class="tag tag-warning">周报</span></td>
                <td>2023-07-16 10:30</td>
                <td><span class="tag tag-success">已完成</span></td>
                <td>
                  <button class="btn" style="color: var(--primary-color);"><i class="fas fa-eye"></i></button>
                  <button class="btn" style="color: var(--primary-color);"><i class="fas fa-download"></i></button>
                  <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
                </td>
              </tr>
              <tr>
                <td>运营月报_202306</td>
                <td><span class="tag tag-primary">月报</span></td>
                <td>2023-07-01 14:15</td>
                <td><span class="tag tag-success">已完成</span></td>
                <td>
                  <button class="btn" style="color: var(--primary-color);"><i class="fas fa-eye"></i></button>
                  <button class="btn" style="color: var(--primary-color);"><i class="fas fa-download"></i></button>
                  <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
                </td>
              </tr>
              <tr>
                <td>Q2季度报表</td>
                <td><span class="tag tag-danger">季报</span></td>
                <td>2023-07-02 09:45</td>
                <td><span class="tag tag-warning">生成中</span></td>
                <td>
                  <button class="btn" style="color: var(--text-tertiary);" disabled><i class="fas fa-eye"></i></button>
                  <button class="btn" style="color: var(--text-tertiary);" disabled><i class="fas fa-download"></i></button>
                  <button class="btn" style="color: var(--danger-color);"><i class="fas fa-times"></i></button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <script src="js/common.js"></script>
</body>
</html>