<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>服务拓扑 - 数智化运营平台</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <i class="fas fa-chart-line"></i> 数智化运营平台
      </div>
      <div style="display: flex; align-items: center;">
        <div class="dropdown" style="margin-right: 16px;">
          <button class="dropdown-toggle">
            <i class="fas fa-bell"></i>
            <span class="badge">3</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item">
              <div style="font-weight: 500;">新任务通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">您有3个新任务需要处理</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">数据采集完成</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">昨日数据采集已完成</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">系统更新</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">平台将于今晚23:00进行维护</div>
            </div>
          </div>
        </div>
        <div class="dropdown">
          <button class="dropdown-toggle">
            <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px;">
            <span>管理员</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item"><i class="fas fa-user"></i> 个人中心</div>
            <div class="dropdown-item"><i class="fas fa-cog"></i> 系统设置</div>
            <div class="dropdown-item"><i class="fas fa-sign-out-alt"></i> 退出登录</div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- 侧边栏 -->
  <div class="sidebar">
    <div class="menu-item" onclick="navigateToPage('index.html')">
      <i class="fas fa-home menu-icon"></i>
      <span class="menu-text">首页</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-database menu-icon"></i>
      <span class="menu-text">数据融通</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-pie menu-icon"></i>
      <span class="menu-text">智能洞察分析</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-bar menu-icon"></i>
      <span class="menu-text">运营视图</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tachometer-alt menu-icon"></i>
      <span class="menu-text">统一运营门户</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tasks menu-icon"></i>
      <span class="menu-text">五级穿透调度</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-server menu-icon"></i>
      <span class="menu-text">微服务管理</span>
    </div>
    
    <!-- DevOps 模块 -->
    <div class="menu-section">
      <div class="menu-section-title">DevOps 平台</div>
    </div>
    <div class="menu-item" onclick="navigateToPage('devops_dashboard.html')">
      <i class="fas fa-tachometer-alt menu-icon"></i>
      <span class="menu-text">DevOps 总览</span>
    </div>
    <div class="menu-item" onclick="navigateToPage('pipeline_management.html')">
      <i class="fas fa-code-branch menu-icon"></i>
      <span class="menu-text">CI/CD 流水线</span>
    </div>
    <div class="menu-item" onclick="navigateToPage('deployment_management.html')">
      <i class="fas fa-cube menu-icon"></i>
      <span class="menu-text">容器部署</span>
    </div>
    <div class="menu-item" onclick="navigateToPage('monitoring_center.html')">
      <i class="fas fa-chart-line menu-icon"></i>
      <span class="menu-text">监控中心</span>
    </div>
    <div class="menu-item active" onclick="navigateToPage('service_topology.html')">
      <i class="fas fa-project-diagram menu-icon"></i>
      <span class="menu-text">服务拓扑</span>
    </div>
    
    <div class="menu-item">
      <i class="fas fa-user-shield menu-icon"></i>
      <span class="menu-text">权限管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-cog menu-icon"></i>
      <span class="menu-text">系统设置</span>
    </div>
  </div>

  <!-- 主内容区 -->
  <div class="main-content">
    <div class="page-title">
      <i class="fas fa-project-diagram page-title-icon"></i>
      服务拓扑
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit;">首页</a></div>
      <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit;">DevOps 平台</a></div>
      <div class="breadcrumb-item active">服务拓扑</div>
    </div>

    <!-- 页面标题和操作 -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px;">
      <div>
        <h1 style="font-size: 24px; font-weight: bold; margin: 0; color: var(--text-primary);">服务拓扑</h1>
        <p style="color: var(--text-secondary); margin: 4px 0 0 0;">微服务架构拓扑图和服务关系</p>
      </div>
      <div style="display: flex; align-items: center; gap: 8px;">
        <button class="btn" style="border: 1px solid var(--border-color);" onclick="refreshTopology()">
          <i class="fas fa-sync-alt"></i> 刷新
        </button>
        <button class="btn btn-primary" onclick="toggleLayout()">
          <i class="fas fa-expand-arrows-alt"></i> 切换布局
        </button>
      </div>
    </div>

    <!-- 拓扑图控制面板 -->
    <div class="card" style="padding: 16px; margin-bottom: 24px;">
      <div style="display: flex; align-items: center; gap: 16px; flex-wrap: wrap;">
        <div style="display: flex; align-items: center; gap: 8px;">
          <span style="font-size: 14px; font-weight: 500; color: var(--text-primary);">视图:</span>
          <select id="viewMode" style="border: 1px solid var(--border-color); border-radius: 4px; padding: 6px 10px; font-size: 14px;" onchange="changeViewMode()">
            <option value="services">服务视图</option>
            <option value="dependencies">依赖关系</option>
            <option value="traffic">流量视图</option>
          </select>
        </div>
        <div style="display: flex; align-items: center; gap: 8px;">
          <span style="font-size: 14px; font-weight: 500; color: var(--text-primary);">过滤:</span>
          <select id="statusFilter" style="border: 1px solid var(--border-color); border-radius: 4px; padding: 6px 10px; font-size: 14px;" onchange="filterServices()">
            <option value="all">全部服务</option>
            <option value="healthy">健康服务</option>
            <option value="unhealthy">异常服务</option>
          </select>
        </div>
        <div style="display: flex; align-items: center; gap: 8px;">
          <span style="font-size: 14px; color: var(--text-secondary);">图例:</span>
          <div style="display: flex; align-items: center; gap: 12px;">
            <div style="display: flex; align-items: center; gap: 4px;">
              <div style="width: 12px; height: 12px; border-radius: 50%; background-color: var(--success-color);"></div>
              <span style="font-size: 12px; color: var(--text-secondary);">健康</span>
            </div>
            <div style="display: flex; align-items: center; gap: 4px;">
              <div style="width: 12px; height: 12px; border-radius: 50%; background-color: var(--danger-color);"></div>
              <span style="font-size: 12px; color: var(--text-secondary);">异常</span>
            </div>
            <div style="display: flex; align-items: center; gap: 4px;">
              <div style="width: 12px; height: 12px; border-radius: 50%; background-color: var(--warning-color);"></div>
              <span style="font-size: 12px; color: var(--text-secondary);">警告</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 拓扑图容器 -->
    <div class="row">
      <div class="col col-8">
        <div class="card" style="padding: 20px; height: 600px;">
          <div id="topologyContainer" style="width: 100%; height: 100%; position: relative; background-color: var(--bg-color); border-radius: 4px; overflow: hidden;">
            <!-- 拓扑图将通过JavaScript动态生成 -->
          </div>
        </div>
      </div>
      <div class="col col-4">
        <div class="card" style="padding: 20px; height: 600px; overflow-y: auto;">
          <h3 style="font-size: 16px; font-weight: 600; margin: 0 0 16px 0; color: var(--text-primary);">服务详情</h3>
          <div id="serviceDetails">
            <div style="text-align: center; padding: 40px 20px; color: var(--text-tertiary);">
              <i class="fas fa-mouse-pointer" style="font-size: 48px; margin-bottom: 16px;"></i>
              <p style="margin: 0; font-size: 14px;">点击服务节点查看详细信息</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 服务列表 -->
    <div class="card">
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; padding-bottom: 16px; border-bottom: 1px solid var(--border-color);">
        <h3 style="font-size: 18px; font-weight: 600;">服务列表</h3>
        <div style="display: flex; gap: 8px;">
          <input type="text" id="serviceSearch" placeholder="搜索服务..." style="padding: 6px 12px; border: 1px solid var(--border-color); border-radius: 4px; font-size: 14px;" oninput="searchServices()">
        </div>
      </div>
      <div id="servicesList">
        <!-- 服务列表将通过JavaScript动态生成 -->
      </div>
    </div>
  </div>

  <script src="js/common.js"></script>
  <script>
    // 模拟服务数据
    const services = [
      {
        id: 'api-gateway',
        name: 'API Gateway',
        type: 'gateway',
        status: 'healthy',
        instances: 2,
        version: 'v1.0.0',
        cpu: 45,
        memory: 512,
        position: { x: 400, y: 100 },
        dependencies: ['user-service', 'order-service', 'payment-service']
      },
      {
        id: 'user-service',
        name: 'User Service',
        type: 'service',
        status: 'healthy',
        instances: 3,
        version: 'v1.2.3',
        cpu: 35,
        memory: 768,
        position: { x: 200, y: 250 },
        dependencies: ['user-db']
      },
      {
        id: 'order-service',
        name: 'Order Service',
        type: 'service',
        status: 'healthy',
        instances: 5,
        version: 'v2.1.0',
        cpu: 65,
        memory: 1024,
        position: { x: 400, y: 250 },
        dependencies: ['order-db', 'payment-service']
      },
      {
        id: 'payment-service',
        name: 'Payment Service',
        type: 'service',
        status: 'unhealthy',
        instances: 2,
        version: 'v1.5.2',
        cpu: 85,
        memory: 1536,
        position: { x: 600, y: 250 },
        dependencies: ['payment-db', 'external-payment-api']
      },
      {
        id: 'notification-service',
        name: 'Notification Service',
        type: 'service',
        status: 'healthy',
        instances: 2,
        version: 'v1.0.8',
        cpu: 25,
        memory: 256,
        position: { x: 800, y: 250 },
        dependencies: ['message-queue']
      },
      {
        id: 'user-db',
        name: 'User Database',
        type: 'database',
        status: 'healthy',
        instances: 1,
        version: 'PostgreSQL 13',
        cpu: 20,
        memory: 2048,
        position: { x: 200, y: 400 },
        dependencies: []
      },
      {
        id: 'order-db',
        name: 'Order Database',
        type: 'database',
        status: 'healthy',
        instances: 1,
        version: 'PostgreSQL 13',
        cpu: 30,
        memory: 4096,
        position: { x: 400, y: 400 },
        dependencies: []
      },
      {
        id: 'payment-db',
        name: 'Payment Database',
        type: 'database',
        status: 'healthy',
        instances: 1,
        version: 'PostgreSQL 13',
        cpu: 25,
        memory: 2048,
        position: { x: 600, y: 400 },
        dependencies: []
      },
      {
        id: 'message-queue',
        name: 'Message Queue',
        type: 'middleware',
        status: 'healthy',
        instances: 3,
        version: 'RabbitMQ 3.8',
        cpu: 15,
        memory: 512,
        position: { x: 800, y: 400 },
        dependencies: []
      },
      {
        id: 'external-payment-api',
        name: 'External Payment API',
        type: 'external',
        status: 'warning',
        instances: 1,
        version: 'v2.0',
        cpu: 0,
        memory: 0,
        position: { x: 600, y: 500 },
        dependencies: []
      }
    ];

    let selectedService = null;
    let currentLayout = 'hierarchical';

    // 获取服务状态颜色
    function getServiceStatusColor(status) {
      switch (status) {
        case 'healthy': return 'var(--success-color)';
        case 'unhealthy': return 'var(--danger-color)';
        case 'warning': return 'var(--warning-color)';
        default: return 'var(--text-tertiary)';
      }
    }

    // 获取服务类型图标
    function getServiceTypeIcon(type) {
      switch (type) {
        case 'gateway': return 'fas fa-door-open';
        case 'service': return 'fas fa-cube';
        case 'database': return 'fas fa-database';
        case 'middleware': return 'fas fa-cogs';
        case 'external': return 'fas fa-external-link-alt';
        default: return 'fas fa-question-circle';
      }
    }

    // 渲染拓扑图
    function renderTopology() {
      const container = document.getElementById('topologyContainer');
      container.innerHTML = '';

      // 创建SVG容器
      const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
      svg.setAttribute('width', '100%');
      svg.setAttribute('height', '100%');
      svg.style.position = 'absolute';
      svg.style.top = '0';
      svg.style.left = '0';

      // 绘制连接线
      services.forEach(service => {
        service.dependencies.forEach(depId => {
          const depService = services.find(s => s.id === depId);
          if (depService) {
            const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            line.setAttribute('x1', service.position.x + 50);
            line.setAttribute('y1', service.position.y + 50);
            line.setAttribute('x2', depService.position.x + 50);
            line.setAttribute('y2', depService.position.y + 50);
            line.setAttribute('stroke', '#e5e7eb');
            line.setAttribute('stroke-width', '2');
            line.setAttribute('marker-end', 'url(#arrowhead)');
            svg.appendChild(line);
          }
        });
      });

      // 添加箭头标记
      const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
      const marker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
      marker.setAttribute('id', 'arrowhead');
      marker.setAttribute('markerWidth', '10');
      marker.setAttribute('markerHeight', '7');
      marker.setAttribute('refX', '9');
      marker.setAttribute('refY', '3.5');
      marker.setAttribute('orient', 'auto');
      const polygon = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
      polygon.setAttribute('points', '0 0, 10 3.5, 0 7');
      polygon.setAttribute('fill', '#e5e7eb');
      marker.appendChild(polygon);
      defs.appendChild(marker);
      svg.appendChild(defs);

      container.appendChild(svg);

      // 绘制服务节点
      services.forEach(service => {
        const node = document.createElement('div');
        node.className = 'service-node';
        node.setAttribute('data-service-id', service.id);
        node.style.position = 'absolute';
        node.style.left = service.position.x + 'px';
        node.style.top = service.position.y + 'px';
        node.style.width = '100px';
        node.style.height = '100px';
        node.style.backgroundColor = 'white';
        node.style.border = `3px solid ${getServiceStatusColor(service.status)}`;
        node.style.borderRadius = '8px';
        node.style.padding = '8px';
        node.style.cursor = 'pointer';
        node.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
        node.style.transition = 'all 0.2s';
        node.style.display = 'flex';
        node.style.flexDirection = 'column';
        node.style.alignItems = 'center';
        node.style.justifyContent = 'center';
        node.style.textAlign = 'center';

        node.innerHTML = `
          <i class="${getServiceTypeIcon(service.type)}" style="font-size: 24px; color: ${getServiceStatusColor(service.status)}; margin-bottom: 4px;"></i>
          <div style="font-size: 10px; font-weight: 600; color: var(--text-primary); line-height: 1.2; word-break: break-word;">
            ${service.name}
          </div>
          <div style="font-size: 8px; color: var(--text-tertiary); margin-top: 2px;">
            ${service.instances} 实例
          </div>
        `;

        node.addEventListener('click', () => selectService(service.id));
        node.addEventListener('mouseenter', () => {
          node.style.transform = 'scale(1.05)';
          node.style.boxShadow = '0 4px 16px rgba(0,0,0,0.2)';
        });
        node.addEventListener('mouseleave', () => {
          node.style.transform = 'scale(1)';
          node.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
        });

        container.appendChild(node);
      });
    }

    // 选择服务
    function selectService(serviceId) {
      selectedService = services.find(s => s.id === serviceId);
      if (selectedService) {
        renderServiceDetails(selectedService);

        // 重置所有服务节点的样式
        document.querySelectorAll('.service-node').forEach(node => {
          node.style.borderWidth = '3px';
          node.classList.remove('selected');
        });

        // 找到对应的服务节点并高亮
        const selectedNode = document.querySelector(`[data-service-id="${serviceId}"]`);
        if (selectedNode) {
          selectedNode.style.borderWidth = '4px';
          selectedNode.classList.add('selected');
        }
      }
    }

    // 渲染服务详情
    function renderServiceDetails(service) {
      const container = document.getElementById('serviceDetails');
      container.innerHTML = `
        <div style="border-left: 3px solid ${getServiceStatusColor(service.status)}; padding-left: 12px; margin-bottom: 16px;">
          <h4 style="margin: 0 0 4px 0; font-size: 16px; font-weight: 600; color: var(--text-primary);">
            ${service.name}
          </h4>
          <div style="font-size: 12px; color: var(--text-secondary);">
            ${service.type.charAt(0).toUpperCase() + service.type.slice(1)} • ${service.version}
          </div>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-bottom: 16px;">
          <div style="text-align: center; padding: 8px; background-color: var(--bg-color); border-radius: 4px;">
            <div style="font-size: 18px; font-weight: 600; color: var(--text-primary);">${service.instances}</div>
            <div style="font-size: 12px; color: var(--text-secondary);">实例数</div>
          </div>
          <div style="text-align: center; padding: 8px; background-color: var(--bg-color); border-radius: 4px;">
            <div style="font-size: 18px; font-weight: 600; color: ${getServiceStatusColor(service.status)};">
              ${service.status === 'healthy' ? '健康' : service.status === 'unhealthy' ? '异常' : '警告'}
            </div>
            <div style="font-size: 12px; color: var(--text-secondary);">状态</div>
          </div>
        </div>

        <div style="margin-bottom: 16px;">
          <h5 style="margin: 0 0 8px 0; font-size: 14px; font-weight: 600; color: var(--text-primary);">资源使用</h5>
          <div style="margin-bottom: 8px;">
            <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
              <span style="font-size: 12px; color: var(--text-secondary);">CPU</span>
              <span style="font-size: 12px; color: var(--text-primary);">${service.cpu}%</span>
            </div>
            <div style="width: 100%; height: 6px; background-color: var(--bg-color); border-radius: 3px; overflow: hidden;">
              <div style="height: 100%; background-color: ${service.cpu > 80 ? 'var(--danger-color)' : service.cpu > 60 ? 'var(--warning-color)' : 'var(--success-color)'}; width: ${service.cpu}%;"></div>
            </div>
          </div>
          <div>
            <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
              <span style="font-size: 12px; color: var(--text-secondary);">内存</span>
              <span style="font-size: 12px; color: var(--text-primary);">${service.memory}MB</span>
            </div>
            <div style="width: 100%; height: 6px; background-color: var(--bg-color); border-radius: 3px; overflow: hidden;">
              <div style="height: 100%; background-color: ${service.memory > 1500 ? 'var(--danger-color)' : service.memory > 1000 ? 'var(--warning-color)' : 'var(--success-color)'}; width: ${Math.min(100, (service.memory / 2048) * 100)}%;"></div>
            </div>
          </div>
        </div>

        ${service.dependencies.length > 0 ? `
          <div>
            <h5 style="margin: 0 0 8px 0; font-size: 14px; font-weight: 600; color: var(--text-primary);">依赖服务</h5>
            <div style="display: flex; flex-direction: column; gap: 4px;" id="dependencyList">
              ${service.dependencies.map(depId => {
                const depService = services.find(s => s.id === depId);
                return depService ? `
                  <div class="dependency-item" data-service-id="${depService.id}" style="display: flex; align-items: center; gap: 8px; padding: 4px 8px; background-color: var(--bg-color); border-radius: 4px; cursor: pointer; transition: background-color 0.2s;">
                    <i class="${getServiceTypeIcon(depService.type)}" style="color: ${getServiceStatusColor(depService.status)}; font-size: 12px;"></i>
                    <span style="font-size: 12px; color: var(--text-primary);">${depService.name}</span>
                  </div>
                ` : '';
              }).join('')}
            </div>
          </div>
        ` : ''}

        <div style="margin-top: 16px; padding-top: 16px; border-top: 1px solid var(--border-color);">
          <button class="btn btn-primary" style="width: 100%; font-size: 12px; padding: 8px;" onclick="viewServiceLogs('${service.id}')">
            <i class="fas fa-file-alt"></i> 查看日志
          </button>
        </div>
      `;

      // 为依赖服务项添加点击事件监听器
      setTimeout(() => {
        const dependencyItems = document.querySelectorAll('.dependency-item');
        dependencyItems.forEach(item => {
          item.addEventListener('click', () => {
            const serviceId = item.getAttribute('data-service-id');
            selectService(serviceId);
          });

          // 添加悬停效果
          item.addEventListener('mouseenter', () => {
            item.style.backgroundColor = 'var(--hover-color)';
          });
          item.addEventListener('mouseleave', () => {
            item.style.backgroundColor = 'var(--bg-color)';
          });
        });
      }, 0);
    }

    // 渲染服务列表
    function renderServicesList() {
      const container = document.getElementById('servicesList');
      container.innerHTML = services.map(service => `
        <div class="service-list-item" data-service-id="${service.id}" style="display: flex; align-items: center; justify-content: space-between; padding: 12px; border-bottom: 1px solid var(--border-color); cursor: pointer; transition: background-color 0.2s;">
          <div style="display: flex; align-items: center; gap: 12px;">
            <i class="${getServiceTypeIcon(service.type)}" style="color: ${getServiceStatusColor(service.status)}; font-size: 16px;"></i>
            <div>
              <div style="font-size: 14px; font-weight: 500; color: var(--text-primary);">
                ${service.name}
              </div>
              <div style="font-size: 12px; color: var(--text-secondary);">
                ${service.type} • ${service.version} • ${service.instances} 实例
              </div>
            </div>
          </div>
          <div style="display: flex; align-items: center; gap: 8px;">
            <span style="background-color: ${getServiceStatusColor(service.status)}; color: white; padding: 2px 6px; border-radius: 4px; font-size: 10px; font-weight: 500;">
              ${service.status === 'healthy' ? '健康' : service.status === 'unhealthy' ? '异常' : '警告'}
            </span>
            <div style="font-size: 12px; color: var(--text-tertiary);">
              CPU: ${service.cpu}%
            </div>
          </div>
        </div>
      `).join('');

      // 为服务列表项添加事件监听器
      setTimeout(() => {
        const serviceListItems = document.querySelectorAll('.service-list-item');
        serviceListItems.forEach(item => {
          item.addEventListener('click', () => {
            const serviceId = item.getAttribute('data-service-id');
            selectService(serviceId);
          });

          // 添加悬停效果
          item.addEventListener('mouseenter', () => {
            item.style.backgroundColor = 'var(--bg-color)';
          });
          item.addEventListener('mouseleave', () => {
            item.style.backgroundColor = 'transparent';
          });
        });
      }, 0);
    }

    // 搜索服务
    function searchServices() {
      const searchTerm = document.getElementById('serviceSearch').value.toLowerCase();
      const filteredServices = services.filter(service =>
        service.name.toLowerCase().includes(searchTerm) ||
        service.type.toLowerCase().includes(searchTerm)
      );

      const container = document.getElementById('servicesList');
      container.innerHTML = filteredServices.map(service => `
        <div class="service-list-item" data-service-id="${service.id}" style="display: flex; align-items: center; justify-content: space-between; padding: 12px; border-bottom: 1px solid var(--border-color); cursor: pointer; transition: background-color 0.2s;">
          <div style="display: flex; align-items: center; gap: 12px;">
            <i class="${getServiceTypeIcon(service.type)}" style="color: ${getServiceStatusColor(service.status)}; font-size: 16px;"></i>
            <div>
              <div style="font-size: 14px; font-weight: 500; color: var(--text-primary);">
                ${service.name}
              </div>
              <div style="font-size: 12px; color: var(--text-secondary);">
                ${service.type} • ${service.version} • ${service.instances} 实例
              </div>
            </div>
          </div>
          <div style="display: flex; align-items: center; gap: 8px;">
            <span style="background-color: ${getServiceStatusColor(service.status)}; color: white; padding: 2px 6px; border-radius: 4px; font-size: 10px; font-weight: 500;">
              ${service.status === 'healthy' ? '健康' : service.status === 'unhealthy' ? '异常' : '警告'}
            </span>
            <div style="font-size: 12px; color: var(--text-tertiary);">
              CPU: ${service.cpu}%
            </div>
          </div>
        </div>
      `).join('');

      // 为搜索结果添加事件监听器
      setTimeout(() => {
        const serviceListItems = document.querySelectorAll('.service-list-item');
        serviceListItems.forEach(item => {
          item.addEventListener('click', () => {
            const serviceId = item.getAttribute('data-service-id');
            selectService(serviceId);
          });

          // 添加悬停效果
          item.addEventListener('mouseenter', () => {
            item.style.backgroundColor = 'var(--bg-color)';
          });
          item.addEventListener('mouseleave', () => {
            item.style.backgroundColor = 'transparent';
          });
        });
      }, 0);
    }

    // 其他功能
    function refreshTopology() {
      renderTopology();
      alert('拓扑图已刷新');
    }

    function toggleLayout() {
      currentLayout = currentLayout === 'hierarchical' ? 'circular' : 'hierarchical';
      if (currentLayout === 'circular') {
        // 重新排列为圆形布局
        const centerX = 400;
        const centerY = 300;
        const radius = 200;
        services.forEach((service, index) => {
          const angle = (index / services.length) * 2 * Math.PI;
          service.position.x = centerX + Math.cos(angle) * radius - 50;
          service.position.y = centerY + Math.sin(angle) * radius - 50;
        });
      } else {
        // 恢复层次布局
        const positions = [
          { x: 400, y: 100 }, // api-gateway
          { x: 200, y: 250 }, // user-service
          { x: 400, y: 250 }, // order-service
          { x: 600, y: 250 }, // payment-service
          { x: 800, y: 250 }, // notification-service
          { x: 200, y: 400 }, // user-db
          { x: 400, y: 400 }, // order-db
          { x: 600, y: 400 }, // payment-db
          { x: 800, y: 400 }, // message-queue
          { x: 600, y: 500 }  // external-payment-api
        ];
        services.forEach((service, index) => {
          if (positions[index]) {
            service.position = positions[index];
          }
        });
      }
      renderTopology();
    }

    function changeViewMode() {
      const mode = document.getElementById('viewMode').value;
      alert('切换到' + mode + '视图');
    }

    function filterServices() {
      const filter = document.getElementById('statusFilter').value;
      alert('过滤服务: ' + filter);
    }

    function viewServiceLogs(serviceId) {
      alert('查看服务日志: ' + serviceId);
    }

    // 初始化页面
    document.addEventListener('DOMContentLoaded', function() {
      renderTopology();
      renderServicesList();
    });
  </script>

  <style>
    .service-node {
      user-select: none;
    }
  </style>
</body>
</html>
