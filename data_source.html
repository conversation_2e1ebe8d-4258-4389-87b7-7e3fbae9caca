<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>数智化运营平台 - 数据源管理</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <i class="fas fa-chart-line"></i> 数智化运营平台
      </div>
      <div style="display: flex; align-items: center;">
        <div class="dropdown" style="margin-right: 16px;">
          <button class="dropdown-toggle">
            <i class="fas fa-bell"></i>
            <span class="badge">3</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item">
              <div style="font-weight: 500;">新任务通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">您有3个新任务需要处理</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">数据采集完成</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">昨日数据采集已完成</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">系统更新</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">平台将于今晚23:00进行维护</div>
            </div>
          </div>
        </div>
        <div class="dropdown">
          <button class="dropdown-toggle">
            <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px;">
            <span>管理员</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item"><i class="fas fa-user"></i> 个人中心</div>
            <div class="dropdown-item"><i class="fas fa-cog"></i> 系统设置</div>
            <div class="dropdown-item"><i class="fas fa-sign-out-alt"></i> 退出登录</div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- 侧边栏 -->
  <div class="sidebar">
    <div class="menu-item">
      <i class="fas fa-home menu-icon"></i>
      <span class="menu-text">首页</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-database menu-icon"></i>
      <span class="menu-text">数据融通</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-pie menu-icon"></i>
      <span class="menu-text">智能洞察分析</span>
    </div>
    <div class="menu-item active">
      <i class="fas fa-chart-bar menu-icon"></i>
      <span class="menu-text">运营视图</span>
      <div style="margin-left: 24px; margin-top: 8px; font-size: 14px;">
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">预置大屏模板</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">预制组件</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">模板管理</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">画布管理</div>
        <div style="padding: 8px 0; color: var(--primary-color); cursor: pointer;">数据源设置</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">自定义报表</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">报表管理</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">报表设计</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">智能生成报表</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">视图操作能力</div>
      </div>
    </div>
    <div class="menu-item">
      <i class="fas fa-tachometer-alt menu-icon"></i>
      <span class="menu-text">统一运营门户</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tasks menu-icon"></i>
      <span class="menu-text">五级穿透调度</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-server menu-icon"></i>
      <span class="menu-text">微服务管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-user-shield menu-icon"></i>
      <span class="menu-text">权限管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-cog menu-icon"></i>
      <span class="menu-text">系统设置</span>
    </div>
  </div>

  <!-- 主内容区 -->
  <div class="main-content">
    <div class="page-title">
      <i class="fas fa-database page-title-icon"></i>
      数据源管理
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit;">首页</a></div>
      <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit;">运营视图</a></div>
      <div class="breadcrumb-item active">数据源设置</div>
    </div>

    <!-- 搜索和操作栏 -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
      <div class="search-box" style="width: 300px; margin-bottom: 0;">
        <i class="fas fa-search search-box-icon"></i>
        <input type="text" placeholder="搜索数据源...">
      </div>
      <div style="display: flex;">
        <div style="margin-right: 12px;">
          <select style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
            <option value="all">全部类型</option>
            <option value="mysql">MySQL</option>
            <option value="oracle">Oracle</option>
            <option value="file">文件</option>
            <option value="ftp">FTP</option>
            <option value="api">API</option>
            <option value="kafka">Kafka</option>
          </select>
        </div>
        <button class="btn btn-primary" data-modal-target="addDataSourceModal"><i class="fas fa-plus"></i> 新增数据源</button>
      </div>
    </div>

    <!-- 数据源表格 -->
    <div class="card">
      <div class="table-container">
        <table class="table">
          <thead>
            <tr>
              <th>数据源名称</th>
              <th>类型</th>
              <th>连接信息</th>
              <th>状态</th>
              <th>创建时间</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>用户数据库</td>
              <td><span class="tag tag-info">MySQL</span></td>
              <td>localhost:3306</td>
              <td><span class="tag tag-success">已连接</span></td>
              <td>2023-07-01 10:30</td>
              <td>
                <button class="btn" style="color: var(--primary-color);" data-modal-target="editDataSourceModal"><i class="fas fa-edit"></i></button>
                <button class="btn" style="color: var(--primary-color);"><i class="fas fa-link"></i></button>
                <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
              </td>
            </tr>
            <tr>
              <td>销售数据库</td>
              <td><span class="tag tag-warning">Oracle</span></td>
              <td>*************:1521</td>
              <td><span class="tag tag-success">已连接</span></td>
              <td>2023-07-02 14:15</td>
              <td>
                <button class="btn" style="color: var(--primary-color);" data-modal-target="editDataSourceModal"><i class="fas fa-edit"></i></button>
                <button class="btn" style="color: var(--primary-color);"><i class="fas fa-link"></i></button>
                <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
              </td>
            </tr>
            <tr>
              <td>日志文件</td>
              <td><span class="tag tag-primary">文件</span></td>
              <td>/data/logs/app.log</td>
              <td><span class="tag tag-success">已连接</span></td>
              <td>2023-07-03 09:45</td>
              <td>
                <button class="btn" style="color: var(--primary-color);" data-modal-target="editDataSourceModal"><i class="fas fa-edit"></i></button>
                <button class="btn" style="color: var(--primary-color);"><i class="fas fa-link"></i></button>
                <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
              </td>
            </tr>
            <tr>
              <td>报表文件</td>
              <td><span class="tag tag-danger">FTP</span></td>
              <td>ftp.example.com:21</td>
              <td><span class="tag tag-warning">未连接</span></td>
              <td>2023-07-05 16:20</td>
              <td>
                <button class="btn" style="color: var(--primary-color);" data-modal-target="editDataSourceModal"><i class="fas fa-edit"></i></button>
                <button class="btn" style="color: var(--primary-color);"><i class="fas fa-link"></i></button>
                <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
              </td>
            </tr>
            <tr>
              <td>第三方API</td>
              <td><span class="tag tag-success">API</span></td>
              <td>https://api.example.com</td>
              <td><span class="tag tag-success">已连接</span></td>
              <td>2023-07-08 11:05</td>
              <td>
                <button class="btn" style="color: var(--primary-color);" data-modal-target="editDataSourceModal"><i class="fas fa-edit"></i></button>
                <button class="btn" style="color: var(--primary-color);"><i class="fas fa-link"></i></button>
                <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
              </td>
            </tr>
            <tr>
              <td>实时数据流</td>
              <td><span class="tag tag-info">Kafka</span></td>
              <td>192.168.1.101:9092</td>
              <td><span class="tag tag-success">已连接</span></td>
              <td>2023-07-10 15:30</td>
              <td>
                <button class="btn" style="color: var(--primary-color);" data-modal-target="editDataSourceModal"><i class="fas fa-edit"></i></button>
                <button class="btn" style="color: var(--primary-color);"><i class="fas fa-link"></i></button>
                <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="pagination">
        <div class="pagination-item"><i class="fas fa-chevron-left"></i></div>
        <div class="pagination-item active">1</div>
        <div class="pagination-item">2</div>
        <div class="pagination-item">3</div>
        <div class="pagination-item">4</div>
        <div class="pagination-item">5</div>
        <div class="pagination-item"><i class="fas fa-chevron-right"></i></div>
      </div>
    </div>
  </div>

  <!-- 新增数据源模态框 -->
  <div class="modal" id="addDataSourceModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-plus"></i> 新增数据源</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <form id="addDataSourceForm">
          <div class="form-group">
            <label for="dsName">数据源名称</label>
            <input type="text" id="dsName" name="dsName" required placeholder="请输入数据源名称">
          </div>
          <div class="form-group">
            <label for="dsType">数据源类型</label>
            <select id="dsType" name="dsType" required onchange="changeDataSourceForm(this.value)">
              <option value="">请选择数据源类型</option>
              <option value="mysql">MySQL</option>
              <option value="oracle">Oracle</option>
              <option value="file">文件</option>
              <option value="ftp">FTP</option>
              <option value="api">API</option>
              <option value="kafka">Kafka</option>
            </select>
          </div>
          <div id="mysqlConfig" style="display: none;">
            <div class="form-group">
              <label for="mysqlHost">主机地址</label>
              <input type="text" id="mysqlHost" name="mysqlHost" placeholder="请输入主机地址">
            </div>
            <div class="form-group">
              <label for="mysqlPort">端口</label>
              <input type="number" id="mysqlPort" name="mysqlPort" placeholder="请输入端口" value="3306">
            </div>
            <div class="form-group">
              <label for="mysqlDatabase">数据库名</label>
              <input type="text" id="mysqlDatabase" name="mysqlDatabase" placeholder="请输入数据库名">
            </div>
            <div class="form-group">
              <label for="mysqlUsername">用户名</label>
              <input type="text" id="mysqlUsername" name="mysqlUsername" placeholder="请输入用户名">
            </div>
            <div class="form-group">
              <label for="mysqlPassword">密码</label>
              <input type="password" id="mysqlPassword" name="mysqlPassword" placeholder="请输入密码">
            </div>
            <div class="form-group">
              <label for="mysqlCharset">字符集</label>
              <input type="text" id="mysqlCharset" name="mysqlCharset" placeholder="请输入字符集" value="utf8mb4">
            </div>
          </div>
          <div id="oracleConfig" style="display: none;">
            <div class="form-group">
              <label for="oracleHost">主机地址</label>
              <input type="text" id="oracleHost" name="oracleHost" placeholder="请输入主机地址">
            </div>
            <div class="form-group">
              <label for="oraclePort">端口</label>
              <input type="number" id="oraclePort" name="oraclePort" placeholder="请输入端口" value="1521">
            </div>
            <div class="form-group">
              <label for="oracleServiceName">服务名</label>
              <input type="text" id="oracleServiceName" name="oracleServiceName" placeholder="请输入服务名">
            </div>
            <div class="form-group">
              <label for="oracleUsername">用户名</label>
              <input type="text" id="oracleUsername" name="oracleUsername" placeholder="请输入用户名">
            </div>
            <div class="form-group">
              <label for="oraclePassword">密码</label>
              <input type="password" id="oraclePassword" name="oraclePassword" placeholder="请输入密码">
            </div>
          </div>
          <div id="fileConfig" style="display: none;">
            <div class="form-group">
              <label for="filePath">文件路径</label>
              <input type="text" id="filePath" name="filePath" placeholder="请输入文件路径">
            </div>
            <div class="form-group">
              <label for="fileFormat">文件格式</label>
              <select id="fileFormat" name="fileFormat">
                <option value="csv">CSV</option>
                <option value="json">JSON</option>
                <option value="excel">Excel</option>
                <option value="txt">文本</option>
              </select>
            </div>
            <div class="form-group">
              <label for="fileEncoding">编码</label>
              <input type="text" id="fileEncoding" name="fileEncoding" placeholder="请输入编码" value="utf-8">
            </div>
          </div>
          <div id="ftpConfig" style="display: none;">
            <div class="form-group">
              <label for="ftpHost">主机地址</label>
              <input type="text" id="ftpHost" name="ftpHost" placeholder="请输入主机地址">
            </div>
            <div class="form-group">
              <label for="ftpPort">端口</label>
              <input type="number" id="ftpPort" name="ftpPort" placeholder="请输入端口" value="21">
            </div>
            <div class="form-group">
              <label for="ftpUsername">用户名</label>
              <input type="text" id="ftpUsername" name="ftpUsername" placeholder="请输入用户名">
            </div>
            <div class="form-group">
              <label for="ftpPassword">密码</label>
              <input type="password" id="ftpPassword" name="ftpPassword" placeholder="请输入密码">
            </div>
            <div class="form-group">
              <label for="ftpPath">文件路径</label>
              <input type="text" id="ftpPath" name="ftpPath" placeholder="请输入文件路径">
            </div>
          </div>
          <div id="apiConfig" style="display: none;">
            <div class="form-group">
              <label for="apiUrl">API地址</label>
              <input type="text" id="apiUrl" name="apiUrl" placeholder="请输入API地址">
            </div>
            <div class="form-group">
              <label for="apiMethod">请求方法</label>
              <select id="apiMethod" name="apiMethod">
                <option value="GET">GET</option>
                <option value="POST">POST</option>
                <option value="PUT">PUT</option>
                <option value="DELETE">DELETE</option>
              </select>
            </div>
            <div class="form-group">
              <label for="apiHeaders">请求头</label>
              <textarea id="apiHeaders" name="apiHeaders" rows="3" placeholder="请输入请求头，JSON格式"></textarea>
            </div>
            <div class="form-group">
              <label for="apiParams">请求参数</label>
              <textarea id="apiParams" name="apiParams" rows="3" placeholder="请输入请求参数，JSON格式"></textarea>
            </div>
            <div class="form-group">
              <label for="apiAuthType">认证方式</label>
              <select id="apiAuthType" name="apiAuthType">
                <option value="none">无认证</option>
                <option value="basic">Basic Auth</option>
                <option value="token">Token</option>
              </select>
            </div>
            <div id="apiBasicAuth" style="display: none;">
              <div class="form-group">
                <label for="apiUsername">用户名</label>
                <input type="text" id="apiUsername" name="apiUsername" placeholder="请输入用户名">
              </div>
              <div class="form-group">
                <label for="apiPassword">密码</label>
                <input type="password" id="apiPassword" name="apiPassword" placeholder="请输入密码">
              </div>
            </div>
            <div id="apiTokenAuth" style="display: none;">
              <div class="form-group">
                <label for="apiToken">Token</label>
                <input type="text" id="apiToken" name="apiToken" placeholder="请输入Token">
              </div>
            </div>
          </div>
          <div id="kafkaConfig" style="display: none;">
            <div class="form-group">
              <label for="kafkaBrokers">Broker列表</label>
              <input type="text" id="kafkaBrokers" name="kafkaBrokers" placeholder="请输入Broker列表，多个用逗号分隔">
            </div>
            <div class="form-group">
              <label for="kafkaTopic">Topic</label>
              <input type="text" id="kafkaTopic" name="kafkaTopic" placeholder="请输入Topic">
            </div>
            <div class="form-group">
              <label for="kafkaGroupId">消费组ID</label>
              <input type="text" id="kafkaGroupId" name="kafkaGroupId" placeholder="请输入消费组ID">
            </div>
            <div class="form-group">
              <label for="kafkaAutoOffsetReset">Offset重置策略</label>
              <select id="kafkaAutoOffsetReset" name="kafkaAutoOffsetReset">
                <option value="latest">latest</option>
                <option value="earliest">earliest</option>
                <option value="none">none</option>
              </select>
            </div>
          </div>
          <div class="form-group">
            <label for="dsDescription">数据源描述</label>
            <textarea id="dsDescription" name="dsDescription" rows="3" placeholder="请输入数据源描述"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('addDataSourceModal').classList.remove('show')">取消</button>
        <button class="btn btn-primary" onclick="document.getElementById('addDataSourceForm').submit()">测试连接并保存</button>
      </div>
    </div>
  </div>

  <!-- 编辑数据源模态框 -->
  <div class="modal" id="editDataSourceModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-edit"></i> 编辑数据源</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <!-- 编辑表单内容与新增表单类似，这里省略 -->
        <div style="text-align: center; padding: 20px;">
          <p>编辑数据源表单内容与新增表单类似，实际应用中会根据选择的数据源类型动态加载对应表单。</p>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('editDataSourceModal').classList.remove('show')">取消</button>
        <button class="btn btn-primary">测试连接并保存</button>
      </div>
    </div>
  </div>

  <script src="js/common.js"></script>
  <script>
    // 切换数据源类型表单
    function changeDataSourceForm(type) {
      // 隐藏所有配置表单
      document.getElementById('mysqlConfig').style.display = 'none';
      document.getElementById('oracleConfig').style.display = 'none';
      document.getElementById('fileConfig').style.display = 'none';
      document.getElementById('ftpConfig').style.display = 'none';
      document.getElementById('apiConfig').style.display = 'none';
      document.getElementById('kafkaConfig').style.display = 'none';
      document.getElementById('apiBasicAuth').style.display = 'none';
      document.getElementById('apiTokenAuth').style.display = 'none';

      // 显示选中的配置表单
      if (type) {
        document.getElementById(type + 'Config').style.display = 'block';
      }
    }

    // API认证方式切换
    document.getElementById('apiAuthType').addEventListener('change', function() {
      const authType = this.value;
      document.getElementById('apiBasicAuth').style.display = 'none';
      document.getElementById('apiTokenAuth').style.display = 'none';

      if (authType === 'basic') {
        document.getElementById('apiBasicAuth').style.display = 'block';
      } else if (authType === 'token') {
        document.getElementById('apiTokenAuth').style.display = 'block';
      }
    });

    // 新增数据源表单提交
    document.getElementById('addDataSourceForm').addEventListener('submit', function(e) {
      e.preventDefault();
      if (validateForm('addDataSourceForm')) {
        // 模拟提交成功
        alert('数据源创建成功！');
        document.getElementById('addDataSourceModal').classList.remove('show');
        // 重置表单
        this.reset();
        changeDataSourceForm('');
      }
    });
  </script>
</body>
</html>