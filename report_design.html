<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>报表设计 - 运营视图</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            color: #333;
        }

        /* 内容区样式 */
        .content {
            margin-left: 240px;
            padding: 20px;
            transition: margin-left 0.3s;
        }

        @media (max-width: 768px) {
            .content {
                margin-left: 0;
            }
        }

        /* 操作栏样式 */
        .operation-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }

        .operation-bar .title {
            font-size: 18px;
            font-weight: 600;
        }

        .operation-bar .actions {
            display: flex;
            gap: 10px;
        }

        /* 设计区域样式 */
        .design-container {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        /* 左侧面板样式 */
        .left-panel {
            width: 260px;
            background-color: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .panel-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .panel-item {
            margin-bottom: 15px;
        }

        .panel-item label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            font-size: 14px;
        }

        .panel-item input,
        .panel-item select,
        .panel-item textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .panel-item textarea {
            height: 80px;
            resize: vertical;
        }

        /* 中间设计区域样式 */
        .design-area {
            flex: 1;
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            min-height: 600px;
        }

        .report-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }

        .report-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .report-subtitle {
            font-size: 14px;
            color: #666;
        }

        .report-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px dashed #ddd;
            border-radius: 4px;
            position: relative;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .section-actions {
            position: absolute;
            top: 10px;
            right: 10px;
            display: flex;
            gap: 5px;
        }

        .section-action-btn {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            background-color: #f5f5f5;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 12px;
            color: #666;
        }

        .section-action-btn:hover {
            background-color: #e8e8e8;
        }

        /* 右侧组件库样式 */
        .right-panel {
            width: 260px;
            background-color: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .component-list {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }

        .component-item {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            text-align: center;
            cursor: move;
            background-color: #fafafa;
            transition: all 0.3s;
        }

        .component-item:hover {
            background-color: #e6f7ff;
            border-color: #91d5ff;
        }

        .component-icon {
            font-size: 24px;
            margin-bottom: 5px;
            color: #1890ff;
        }

        .component-name {
            font-size: 12px;
            font-weight: 500;
        }

        /* 按钮样式 */
        .btn {
            display: inline-block;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.3s;
        }

        .btn-primary {
            background-color: #1890ff;
            color: white;
        }

        .btn-primary:hover {
            background-color: #40a9ff;
        }

        .btn-secondary {
            background-color: #f5f5f5;
            color: #333;
        }

        .btn-secondary:hover {
            background-color: #e8e8e8;
        }

        .btn-success {
            background-color: #52c41a;
            color: white;
        }

        .btn-success:hover {
            background-color: #73d13d;
        }

        .btn-danger {
            background-color: #f5222d;
            color: white;
        }

        .btn-danger:hover {
            background-color: #ff4d4f;
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="logo-container">
            <img src="img/logo.png" alt="Logo" class="logo">
            <span class="system-name">政企移动项目管理系统</span>
        </div>
        <div class="user-info">
            <span class="user-name">管理员</span>
            <img src="img/user-avatar.png" alt="User Avatar" class="user-avatar">
        </div>
    </header>

    <!-- 侧边栏 -->
    <aside class="sidebar">
        <div class="menu">
            <div class="menu-item">
                <i class="fas fa-home menu-icon"></i>
                <span class="menu-text">首页</span>
            </div>
            <div class="menu-item">
                <i class="fas fa-database menu-icon"></i>
                <span class="menu-text">数据融通</span>
            </div>
            <div class="menu-item">
                <i class="fas fa-brain menu-icon"></i>
                <span class="menu-text">智能洞察分析</span>
            </div>
            <div class="menu-item active">
                <i class="fas fa-chart-bar menu-icon"></i>
                <span class="menu-text">运营视图</span>
                <div style="margin-left: 24px; margin-top: 8px; font-size: 14px;">
                    <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">预置大屏模板</div>
                    <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">预制组件</div>
                    <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">模板管理</div>
                    <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">画布管理</div>
                    <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">自定义报表</div>
                    <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">报表管理</div>
                    <div style="padding: 8px 0; color: var(--primary-color); cursor: pointer;">报表设计</div>
                    <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">视图操作能力</div>
                </div>
            </div>
            <div class="menu-item">
                <i class="fas fa-desktop menu-icon"></i>
                <span class="menu-text">统一运营门户</span>
            </div>
            <div class="menu-item">
                <i class="fas fa-sitemap menu-icon"></i>
                <span class="menu-text">五级穿透调度</span>
            </div>
            <div class="menu-item">
                <i class="fas fa-microchip menu-icon"></i>
                <span class="menu-text">微服务管理</span>
            </div>
            <div class="menu-item">
                <i class="fas fa-key menu-icon"></i>
                <span class="menu-text">权限管理</span>
            </div>
            <div class="menu-item">
                <i class="fas fa-cog menu-icon"></i>
                <span class="menu-text">系统设置</span>
            </div>
        </div>
    </aside>

    <!-- 主内容区 -->
    <main class="content">
        <div class="operation-bar">
            <div class="title">报表设计</div>
            <div class="actions">
                <button class="btn btn-secondary"><i class="fas fa-save"></i> 保存设计</button>
                <button class="btn btn-secondary"><i class="fas fa-copy"></i> 另存为模板</button>
                <button class="btn btn-primary"><i class="fas fa-play"></i> 预览报表</button>
                <button class="btn btn-success"><i class="fas fa-check"></i> 生成报表</button>
            </div>
        </div>

        <div class="design-container">
            <!-- 左侧属性面板 -->
            <div class="left-panel">
                <div class="panel-title">
                    <i class="fas fa-sliders-h"></i> 属性设置
                </div>

                <div class="panel-item">
                    <label for="report-name">报表名称</label>
                    <input type="text" id="report-name" placeholder="请输入报表名称" value="销售数据分析报表">
                </div>

                <div class="panel-item">
                    <label for="report-desc">报表描述</label>
                    <textarea id="report-desc" placeholder="请输入报表描述">本报表展示了近一年的销售数据，包括销售额、销量、客户分布等关键指标。</textarea>
                </div>

                <div class="panel-item">
                    <label for="data-source">数据源</label>
                    <select id="data-source">
                        <option value="sales">销售数据库</option>
                        <option value="operation">运营数据库</option>
                        <option value="customer">客户数据库</option>
                        <option value="product">产品数据库</option>
                    </select>
                </div>

                <div class="panel-item">
                    <label for="date-range">日期范围</label>
                    <div style="display: flex; gap: 10px;">
                        <input type="date" id="start-date" value="2022-11-01">
                        <span style="align-self: center;">至</span>
                        <input type="date" id="end-date" value="2023-10-31">
                    </div>
                </div>

                <div class="panel-item">
                    <label for="report-format">报表格式</label>
                    <select id="report-format">
                        <option value="pdf">PDF</option>
                        <option value="excel">Excel</option>
                        <option value="word">Word</option>
                        <option value="html">HTML</option>
                    </select>
                </div>
            </div>

            <!-- 中间设计区域 -->
            <div class="design-area">
                <div class="report-header">
                    <div class="report-title" contenteditable="true">销售数据分析报表</div>
                    <div class="report-subtitle" contenteditable="true">2022年11月 - 2023年10月</div>
                </div>

                <div class="report-section">
                    <div class="section-title" contenteditable="true">一、销售概况</div>
                    <div class="section-actions">
                        <button class="section-action-btn"><i class="fas fa-edit"></i></button>
                        <button class="section-action-btn"><i class="fas fa-trash-alt"></i></button>
                    </div>
                    <p contenteditable="true">本季度销售总额达到1,250万元，同比增长15.3%，环比增长8.7%。各产品线均实现不同程度增长，其中智能硬件产品线表现尤为突出，同比增长23.5%。</p>
                </div>

                <div class="report-section">
                    <div class="section-title" contenteditable="true">二、销售趋势</div>
                    <div class="section-actions">
                        <button class="section-action-btn"><i class="fas fa-edit"></i></button>
                        <button class="section-action-btn"><i class="fas fa-trash-alt"></i></button>
                    </div>
                    <div style="height: 300px; background-color: #fafafa; border: 1px solid #eee; border-radius: 4px; display: flex; align-items: center; justify-content: center;">
                        <div style="text-align: center; color: #999;">
                            <i class="fas fa-chart-line" style="font-size: 48px; margin-bottom: 10px;"></i>
                            <p>销售趋势图表</p>
                        </div>
                    </div>
                </div>

                <div class="report-section">
                    <div class="section-title" contenteditable="true">三、产品销售分布</div>
                    <div class="section-actions">
                        <button class="section-action-btn"><i class="fas fa-edit"></i></button>
                        <button class="section-action-btn"><i class="fas fa-trash-alt"></i></button>
                    </div>
                    <div style="height: 300px; background-color: #fafafa; border: 1px solid #eee; border-radius: 4px; display: flex; align-items: center; justify-content: center;">
                        <div style="text-align: center; color: #999;">
                            <i class="fas fa-chart-pie" style="font-size: 48px; margin-bottom: 10px;"></i>
                            <p>产品分布图表</p>
                        </div>
                    </div>
                </div>

                <div class="report-section">
                    <div class="section-title" contenteditable="true">四、客户分析</div>
                    <div class="section-actions">
                        <button class="section-action-btn"><i class="fas fa-edit"></i></button>
                        <button class="section-action-btn"><i class="fas fa-trash-alt"></i></button>
                    </div>
                    <div style="overflow-x: auto;">
                        <table style="width: 100%; border-collapse: collapse;">
                            <thead>
                                <tr>
                                    <th style="border: 1px solid #eee; padding: 10px; text-align: left;">客户名称</th>
                                    <th style="border: 1px solid #eee; padding: 10px; text-align: left;">所属行业</th>
                                    <th style="border: 1px solid #eee; padding: 10px; text-align: left;">采购金额(万元)</th>
                                    <th style="border: 1px solid #eee; padding: 10px; text-align: left;">占比</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td style="border: 1px solid #eee; padding: 10px;">某大型国企</td>
                                    <td style="border: 1px solid #eee; padding: 10px;">制造业</td>
                                    <td style="border: 1px solid #eee; padding: 10px;">320</td>
                                    <td style="border: 1px solid #eee; padding: 10px;">25.6%</td>
                                </tr>
                                <tr>
                                    <td style="border: 1px solid #eee; padding: 10px;">某科技公司</td>
                                    <td style="border: 1px solid #eee; padding: 10px;">信息技术</td>
                                    <td style="border: 1px solid #eee; padding: 10px;">240</td>
                                    <td style="border: 1px solid #eee; padding: 10px;">19.2%</td>
                                </tr>
                                <tr>
                                    <td style="border: 1px solid #eee; padding: 10px;">某金融机构</td>
                                    <td style="border: 1px solid #eee; padding: 10px;">金融服务</td>
                                    <td style="border: 1px solid #eee; padding: 10px;">180</td>
                                    <td style="border: 1px solid #eee; padding: 10px;">14.4%</td>
                                </tr>
                                <tr>
                                    <td style="border: 1px solid #eee; padding: 10px;">某医疗机构</td>
                                    <td style="border: 1px solid #eee; padding: 10px;">医疗健康</td>
                                    <td style="border: 1px solid #eee; padding: 10px;">150</td>
                                    <td style="border: 1px solid #eee; padding: 10px;">12.0%</td>
                                </tr>
                                <tr>
                                    <td style="border: 1px solid #eee; padding: 10px;">其他客户</td>
                                    <td style="border: 1px solid #eee; padding: 10px;">多种行业</td>
                                    <td style="border: 1px solid #eee; padding: 10px;">360</td>
                                    <td style="border: 1px solid #eee; padding: 10px;">28.8%</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 右侧组件库 -->
            <div class="right-panel">
                <div class="panel-title">
                    <i class="fas fa-puzzle-piece"></i> 组件库
                </div>

                <div class="component-list">
                    <div class="component-item">
                        <div class="component-icon"><i class="fas fa-chart-line"></i></div>
                        <div class="component-name">折线图</div>
                    </div>
                    <div class="component-item">
                        <div class="component-icon"><i class="fas fa-chart-bar"></i></div>
                        <div class="component-name">柱状图</div>
                    </div>
                    <div class="component-item">
                        <div class="component-icon"><i class="fas fa-chart-pie"></i></div>
                        <div class="component-name">饼图</div>
                    </div>
                    <div class="component-item">
                        <div class="component-icon"><i class="fas fa-chart-area"></i></div>
                        <div class="component-name">面积图</div>
                    </div>
                    <div class="component-item">
                        <div class="component-icon"><i class="fas fa-table"></i></div>
                        <div class="component-name">表格</div>
                    </div>
                    <div class="component-item">
                        <div class="component-icon"><i class="fas fa-list"></i></div>
                        <div class="component-name">列表</div>
                    </div>
                    <div class="component-item">
                        <div class="component-icon"><i class="fas fa-tags"></i></div>
                        <div class="component-name">标签云</div>
                    </div>
                    <div class="component-item">
                        <div class="component-icon"><i class="fas fa-map-marker-alt"></i></div>
                        <div class="component-name">地图</div>
                    </div>
                    <div class="component-item">
                        <div class="component-icon"><i class="fas fa-percentage"></i></div>
                        <div class="component-name">仪表盘</div>
                    </div>
                    <div class="component-item">
                        <div class="component-icon"><i class="fas fa-image"></i></div>
                        <div class="component-name">图片</div>
                    </div>
                    <div class="component-item">
                        <div class="component-icon"><i class="fas fa-file-alt"></i></div>
                        <div class="component-name">文本</div>
                    </div>
                    <div class="component-item">
                        <div class="component-icon"><i class="fas fa-divide"></i></div>
                        <div class="component-name">分隔线</div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // 侧边栏菜单交互逻辑
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', function() {
                // 移除所有菜单项的active类
                document.querySelectorAll('.menu-item').forEach(menuItem => {
                    menuItem.classList.remove('active');
                });
                // 为当前点击项添加active类
                this.classList.add('active');
            });
        });

        // 区块操作按钮逻辑
        document.querySelectorAll('.section-action-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const action = this.querySelector('i').classList[1];
                const section = this.closest('.report-section');

                if (action === 'fa-trash-alt') {
                    if (confirm('确定要删除这个区块吗？')) {
                        section.remove();
                    }
                } else if (action === 'fa-edit') {
                    // 这里可以添加编辑区块的逻辑
                    alert('编辑区块功能待实现');
                }
            });
        });
    </script>
</body>
</html>