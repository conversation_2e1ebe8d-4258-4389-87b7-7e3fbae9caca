<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>数智化运营平台 - 模板管理</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <i class="fas fa-chart-line"></i> 数智化运营平台
      </div>
      <div style="display: flex; align-items: center;">
        <div class="dropdown" style="margin-right: 16px;">
          <button class="dropdown-toggle">
            <i class="fas fa-bell"></i>
            <span class="badge">3</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item">
              <div style="font-weight: 500;">新任务通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">您有3个新任务需要处理</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">数据采集完成</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">昨日数据采集已完成</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">系统更新</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">平台将于今晚23:00进行维护</div>
            </div>
          </div>
        </div>
        <div class="dropdown">
          <button class="dropdown-toggle">
            <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px;">
            <span>管理员</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item"><i class="fas fa-user"></i> 个人中心</div>
            <div class="dropdown-item"><i class="fas fa-cog"></i> 系统设置</div>
            <div class="dropdown-item"><i class="fas fa-sign-out-alt"></i> 退出登录</div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- 侧边栏 -->
  <div class="sidebar">
    <div class="menu-item">
      <i class="fas fa-home menu-icon"></i>
      <span class="menu-text">首页</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-database menu-icon"></i>
      <span class="menu-text">数据融通</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-pie menu-icon"></i>
      <span class="menu-text">智能洞察分析</span>
    </div>
    <div class="menu-item active">
      <i class="fas fa-chart-bar menu-icon"></i>
      <span class="menu-text">运营视图</span>
      <div style="margin-left: 24px; margin-top: 8px; font-size: 14px;">
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">预置大屏模板</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">预制组件</div>
        <div style="padding: 8px 0; color: var(--primary-color); cursor: pointer;">模板管理</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">画布管理</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">自定义报表</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">视图操作能力</div>
      </div>
    </div>
    <div class="menu-item">
      <i class="fas fa-tachometer-alt menu-icon"></i>
      <span class="menu-text">统一运营门户</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tasks menu-icon"></i>
      <span class="menu-text">五级穿透调度</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-server menu-icon"></i>
      <span class="menu-text">微服务管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-user-shield menu-icon"></i>
      <span class="menu-text">权限管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-cog menu-icon"></i>
      <span class="menu-text">系统设置</span>
    </div>
  </div>

  <!-- 主内容区 -->
  <div class="main-content">
    <div class="page-title">
      <i class="fas fa-folder-open page-title-icon"></i>
      运营视图 - 模板管理
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit;">首页</a></div>
      <div class="breadcrumb-item"><a href="operation_views.html" style="text-decoration: none; color: inherit;">运营视图</a></div>
      <div class="breadcrumb-item active">模板管理</div>
    </div>

    <!-- 筛选和操作栏 -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
      <div style="display: flex;">
        <div style="margin-right: 12px;">
          <select style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
            <option value="all">全部模板</option>
            <option value="system">系统模板</option>
            <option value="custom">自定义模板</option>
          </select>
        </div>
        <div style="margin-right: 12px;">
          <select style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
            <option value="all">全部状态</option>
            <option value="active">启用</option>
            <option value="inactive">禁用</option>
          </select>
        </div>
        <div>
          <div style="display: flex;">
            <input type="text" placeholder="搜索模板名称" style="padding: 6px 12px; border-radius: 4px 0 0 4px; border: 1px solid var(--border-color); border-right: none; width: 200px;">
            <button class="btn btn-primary" style="border-radius: 0 4px 4px 0;"><i class="fas fa-search"></i></button>
          </div>
        </div>
      </div>
      <div style="display: flex;">
        <button class="btn btn-primary" data-modal-target="importTemplateModal"><i class="fas fa-upload"></i> 导入模板</button>
      </div>
    </div>

    <!-- 模板管理表格 -->
    <div class="card" style="overflow-x: auto;">
      <table class="table">
        <thead>
          <tr>
            <th style="width: 50px;"><input type="checkbox"></th>
            <th>模板名称</th>
            <th>模板类型</th>
            <th>创建人</th>
            <th>创建时间</th>
            <th>更新时间</th>
            <th>状态</th>
            <th style="width: 120px;">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td><input type="checkbox"></td>
            <td>全国运营监控大屏</td>
            <td>系统模板</td>
            <td>系统管理员</td>
            <td>2023-09-15 10:30:00</td>
            <td>2023-09-15 10:30:00</td>
            <td><span class="badge success">启用</span></td>
            <td>
              <button class="btn" style="color: var(--primary-color); margin-right: 4px;"><i class="fas fa-edit"></i></button>
              <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash-alt"></i></button>
            </td>
          </tr>
          <tr>
            <td><input type="checkbox"></td>
            <td>省份运营分析大屏</td>
            <td>系统模板</td>
            <td>系统管理员</td>
            <td>2023-09-15 10:30:00</td>
            <td>2023-09-15 10:30:00</td>
            <td><span class="badge success">启用</span></td>
            <td>
              <button class="btn" style="color: var(--primary-color); margin-right: 4px;"><i class="fas fa-edit"></i></button>
              <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash-alt"></i></button>
            </td>
          </tr>
          <tr>
            <td><input type="checkbox"></td>
            <td>自定义销售报表</td>
            <td>自定义模板</td>
            <td>张三</td>
            <td>2023-09-20 14:15:00</td>
            <td>2023-09-22 09:45:00</td>
            <td><span class="badge success">启用</span></td>
            <td>
              <button class="btn" style="color: var(--primary-color); margin-right: 4px;"><i class="fas fa-edit"></i></button>
              <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash-alt"></i></button>
            </td>
          </tr>
          <tr>
            <td><input type="checkbox"></td>
            <td>区域数据对比模板</td>
            <td>自定义模板</td>
            <td>李四</td>
            <td>2023-09-25 16:20:00</td>
            <td>2023-09-25 16:20:00</td>
            <td><span class="badge warning">禁用</span></td>
            <td>
              <button class="btn" style="color: var(--primary-color); margin-right: 4px;"><i class="fas fa-edit"></i></button>
              <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash-alt"></i></button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 分页 -->
    <div style="display: flex; justify-content: flex-end; align-items: center; margin-top: 20px;">
      <div style="font-size: 14px; color: var(--text-tertiary); margin-right: 16px;">共 12 条记录，当前第 1 页</div>
      <div style="display: flex;">
        <button class="btn" style="border: 1px solid var(--border-color); margin-right: 4px;" disabled><i class="fas fa-chevron-left"></i></button>
        <button class="btn btn-primary" style="margin-right: 4px;">1</button>
        <button class="btn" style="border: 1px solid var(--border-color); margin-right: 4px;">2</button>
        <button class="btn" style="border: 1px solid var(--border-color); margin-right: 4px;">3</button>
        <button class="btn" style="border: 1px solid var(--border-color);"><i class="fas fa-chevron-right"></i></button>
      </div>
    </div>
  </div>

  <script src="js/common.js"></script>
</body>
</html>