<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>数智化运营平台 - 实时采集任务管理</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <i class="fas fa-chart-line"></i> 数智化运营平台
      </div>
      <div style="display: flex; align-items: center;">
        <div class="dropdown" style="margin-right: 16px;">
          <button class="dropdown-toggle">
            <i class="fas fa-bell"></i>
            <span class="badge">3</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item">
              <div style="font-weight: 500;">新任务通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">您有3个新任务需要处理</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">数据采集完成</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">昨日数据采集已完成</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">系统更新</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">平台将于今晚23:00进行维护</div>
            </div>
          </div>
        </div>
        <div class="dropdown">
          <button class="dropdown-toggle">
            <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px;">
            <span>管理员</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item"><i class="fas fa-user"></i> 个人中心</div>
            <div class="dropdown-item"><i class="fas fa-cog"></i> 系统设置</div>
            <div class="dropdown-item"><i class="fas fa-sign-out-alt"></i> 退出登录</div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- 侧边栏 -->
  <div class="sidebar">
    <div class="menu-item">
      <i class="fas fa-home menu-icon"></i>
      <span class="menu-text">首页</span>
    </div>
    <div class="menu-item active">
      <i class="fas fa-database menu-icon"></i>
      <span class="menu-text">数据融通</span>
      <div style="margin-left: 24px; margin-top: 8px; font-size: 14px;">
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">数据源管理</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">离线采集任务管理</div>
        <div style="padding: 8px 0; color: var(--primary-color); cursor: pointer;">实时采集任务管理</div>
      </div>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-pie menu-icon"></i>
      <span class="menu-text">智能洞察分析</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-bar menu-icon"></i>
      <span class="menu-text">运营视图</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tachometer-alt menu-icon"></i>
      <span class="menu-text">统一运营门户</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tasks menu-icon"></i>
      <span class="menu-text">五级穿透调度</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-server menu-icon"></i>
      <span class="menu-text">微服务管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-user-shield menu-icon"></i>
      <span class="menu-text">权限管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-cog menu-icon"></i>
      <span class="menu-text">系统设置</span>
    </div>
  </div>

  <!-- 主内容区 -->
  <div class="main-content">
    <div class="page-title">
      <i class="fas fa-bolt page-title-icon"></i>
      实时采集任务管理
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit;">首页</a></div>
      <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit;">数据融通</a></div>
      <div class="breadcrumb-item active">实时采集任务管理</div>
    </div>

    <!-- 搜索和操作栏 -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
      <div class="search-box" style="width: 300px; margin-bottom: 0;">
        <i class="fas fa-search search-box-icon"></i>
        <input type="text" placeholder="搜索任务...">
      </div>
      <div style="display: flex;">
        <div style="margin-right: 12px;">
          <select style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
            <option value="all">全部状态</option>
            <option value="running">运行中</option>
            <option value="stopped">已停止</option>
            <option value="error">异常</option>
          </select>
        </div>
        <button class="btn btn-primary" data-modal-target="addRealtimeTaskModal"><i class="fas fa-plus"></i> 新增任务</button>
      </div>
    </div>

    <!-- 任务列表表格 -->
    <div class="card">
      <div class="table-container">
        <table class="table">
          <thead>
            <tr>
              <th>任务名称</th>
              <th>数据源</th>
              <th>目标表</th>
              <th>状态</th>
              <th>创建时间</th>
              <th>启动时间</th>
              <th>处理速率</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>用户行为数据采集</td>
              <td>Kafka-实时数据流</td>
              <td>dw.user_behavior</td>
              <td><span class="tag tag-success">运行中</span></td>
              <td>2023-07-10 15:30</td>
              <td>2023-07-15 08:00</td>
              <td>1,200 条/秒</td>
              <td>
                <button class="btn" style="color: var(--primary-color);" data-modal-target="editRealtimeTaskModal"><i class="fas fa-edit"></i></button>
                <button class="btn" style="color: var(--warning-color);"><i class="fas fa-pause"></i></button>
                <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
                <button class="btn" style="color: var(--primary-color);"><i class="fas fa-chart-line"></i></button>
              </td>
            </tr>
            <tr>
              <td>产品库存监控</td>
              <td>API-第三方API</td>
              <td>dw.product_inventory</td>
              <td><span class="tag tag-success">运行中</span></td>
              <td>2023-07-12 10:15</td>
              <td>2023-07-14 00:00</td>
              <td>300 条/秒</td>
              <td>
                <button class="btn" style="color: var(--primary-color);" data-modal-target="editRealtimeTaskModal"><i class="fas fa-edit"></i></button>
                <button class="btn" style="color: var(--warning-color);"><i class="fas fa-pause"></i></button>
                <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
                <button class="btn" style="color: var(--primary-color);"><i class="fas fa-chart-line"></i></button>
              </td>
            </tr>
            <tr>
              <td>订单实时处理</td>
              <td>Kafka-实时数据流</td>
              <td>dw.order_process</td>
              <td><span class="tag tag-danger">异常</span></td>
              <td>2023-07-13 14:45</td>
              <td>2023-07-15 09:30</td>
              <td>0 条/秒</td>
              <td>
                <button class="btn" style="color: var(--primary-color);" data-modal-target="editRealtimeTaskModal"><i class="fas fa-edit"></i></button>
                <button class="btn" style="color: var(--primary-color);"><i class="fas fa-redo"></i></button>
                <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
                <button class="btn" style="color: var(--primary-color);"><i class="fas fa-chart-line"></i></button>
              </td>
            </tr>
            <tr>
              <td>设备状态监控</td>
              <td>API-物联网平台</td>
              <td>dw.device_status</td>
              <td><span class="tag tag-warning">已停止</span></td>
              <td>2023-07-14 09:20</td>
              <td>-</td>
              <td>0 条/秒</td>
              <td>
                <button class="btn" style="color: var(--primary-color);" data-modal-target="editRealtimeTaskModal"><i class="fas fa-edit"></i></button>
                <button class="btn" style="color: var(--primary-color);"><i class="fas fa-play"></i></button>
                <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
                <button class="btn" style="color: var(--primary-color);"><i class="fas fa-chart-line"></i></button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="pagination">
        <div class="pagination-item"><i class="fas fa-chevron-left"></i></div>
        <div class="pagination-item active">1</div>
        <div class="pagination-item">2</div>
        <div class="pagination-item">3</div>
        <div class="pagination-item">4</div>
        <div class="pagination-item">5</div>
        <div class="pagination-item"><i class="fas fa-chevron-right"></i></div>
      </div>
    </div>
  </div>

  <!-- 新增实时任务模态框 -->
  <div class="modal" id="addRealtimeTaskModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-plus"></i> 新增实时采集任务</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <form id="addRealtimeTaskForm">
          <div class="form-group">
            <label for="taskName">任务名称</label>
            <input type="text" id="taskName" name="taskName" required placeholder="请输入任务名称">
          </div>
          <div class="form-group">
            <label for="dataSource">数据源</label>
            <select id="dataSource" name="dataSource" required>
              <option value="">请选择数据源</option>
              <option value="kafka_stream">Kafka-实时数据流</option>
              <option value="api_third">API-第三方API</option>
              <option value="api_iot">API-物联网平台</option>
            </select>
          </div>
          <div class="form-group">
            <label for="targetTable">目标表</label>
            <input type="text" id="targetTable" name="targetTable" required placeholder="请输入目标表名">
          </div>
          <div class="form-group">
            <label for="consumerGroup">消费者组</label>
            <input type="text" id="consumerGroup" name="consumerGroup" required placeholder="请输入消费者组名">
          </div>
          <div class="form-group">
            <label for="batchSize">批次大小</label>
            <input type="number" id="batchSize" name="batchSize" required placeholder="请输入批次大小" value="1000">
          </div>
          <div class="form-group">
            <label for="parallelism">并行度</label>
            <input type="number" id="parallelism" name="parallelism" required placeholder="请输入并行度" value="4">
          </div>
          <div class="form-group">
            <label for="dataPreprocess">数据预处理</label>
            <textarea id="dataPreprocess" name="dataPreprocess" rows="3" placeholder="请输入数据预处理脚本"></textarea>
          </div>
          <div class="form-group">
            <label for="securityLevel">安全级别</label>
            <select id="securityLevel" name="securityLevel">
              <option value="low">低</option>
              <option value="medium" selected>中</option>
              <option value="high">高</option>
            </select>
          </div>
          <div class="form-group">
            <label for="taskDescription">任务描述</label>
            <textarea id="taskDescription" name="taskDescription" rows="3" placeholder="请输入任务描述"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('addRealtimeTaskModal').classList.remove('show')">取消</button>
        <button class="btn btn-primary" onclick="document.getElementById('addRealtimeTaskForm').submit()">保存并启动</button>
      </div>
    </div>
  </div>

  <!-- 编辑实时任务模态框 -->
  <div class="modal" id="editRealtimeTaskModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-edit"></i> 编辑实时采集任务</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <!-- 编辑表单内容与新增表单类似，这里省略 -->
        <div style="text-align: center; padding: 20px;">
          <p>编辑实时采集任务表单内容与新增表单类似，实际应用中会加载任务的当前配置。</p>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('editRealtimeTaskModal').classList.remove('show')">取消</button>
        <button class="btn btn-primary">保存修改</button>
      </div>
    </div>
  </div>

  <script src="js/common.js"></script>
  <script>
    // 新增实时任务表单提交
    document.getElementById('addRealtimeTaskForm').addEventListener('submit', function(e) {
      e.preventDefault();
      if (validateForm('addRealtimeTaskForm')) {
        // 模拟提交成功
        alert('实时采集任务创建成功并已启动！');
        document.getElementById('addRealtimeTaskModal').classList.remove('show');
        // 重置表单
        this.reset();
      }
    });
  </script>
</body>
</html>