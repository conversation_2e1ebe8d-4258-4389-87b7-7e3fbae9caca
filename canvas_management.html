<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>数智化运营平台 - 画布管理</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <i class="fas fa-chart-line"></i> 数智化运营平台
      </div>
      <div style="display: flex; align-items: center;">
        <div class="dropdown" style="margin-right: 16px;">
          <button class="dropdown-toggle">
            <i class="fas fa-bell"></i>
            <span class="badge">3</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item">
              <div style="font-weight: 500;">新任务通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">您有3个新任务需要处理</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">数据采集完成</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">昨日数据采集已完成</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">系统更新</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">平台将于今晚23:00进行维护</div>
            </div>
          </div>
        </div>
        <div class="dropdown">
          <button class="dropdown-toggle">
            <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px;">
            <span>管理员</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item"><i class="fas fa-user"></i> 个人中心</div>
            <div class="dropdown-item"><i class="fas fa-cog"></i> 系统设置</div>
            <div class="dropdown-item"><i class="fas fa-sign-out-alt"></i> 退出登录</div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- 侧边栏 -->
  <div class="sidebar">
    <div class="menu-item">
      <i class="fas fa-home menu-icon"></i>
      <span class="menu-text">首页</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-database menu-icon"></i>
      <span class="menu-text">数据融通</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-pie menu-icon"></i>
      <span class="menu-text">智能洞察分析</span>
    </div>
    <div class="menu-item active">
      <i class="fas fa-chart-bar menu-icon"></i>
      <span class="menu-text">运营视图</span>
      <div style="margin-left: 24px; margin-top: 8px; font-size: 14px;">
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">预置大屏模板</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">预制组件</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">模板管理</div>
        <div style="padding: 8px 0; color: var(--primary-color); cursor: pointer;">画布管理</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">自定义报表</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">视图操作能力</div>
      </div>
    </div>
    <div class="menu-item">
      <i class="fas fa-tachometer-alt menu-icon"></i>
      <span class="menu-text">统一运营门户</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tasks menu-icon"></i>
      <span class="menu-text">五级穿透调度</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-server menu-icon"></i>
      <span class="menu-text">微服务管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-user-shield menu-icon"></i>
      <span class="menu-text">权限管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-cog menu-icon"></i>
      <span class="menu-text">系统设置</span>
    </div>
  </div>

  <!-- 主内容区 -->
  <div class="main-content">
    <div class="page-title">
      <i class="fas fa-project-diagram page-title-icon"></i>
      运营视图 - 画布管理
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit;">首页</a></div>
      <div class="breadcrumb-item"><a href="operation_views.html" style="text-decoration: none; color: inherit;">运营视图</a></div>
      <div class="breadcrumb-item active">画布管理</div>
    </div>

    <!-- 筛选和操作栏 -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
      <div style="display: flex;">
        <div style="margin-right: 12px;">
          <select style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
            <option value="all">全部画布</option>
            <option value="personal">个人画布</option>
            <option value="shared">共享画布</option>
          </select>
        </div>
        <div style="margin-right: 12px;">
          <select style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
            <option value="all">全部状态</option>
            <option value="draft">草稿</option>
            <option value="published">已发布</option>
          </select>
        </div>
        <div>
          <div style="display: flex;">
            <input type="text" placeholder="搜索画布名称" style="padding: 6px 12px; border-radius: 4px 0 0 4px; border: 1px solid var(--border-color); border-right: none; width: 200px;">
            <button class="btn btn-primary" style="border-radius: 0 4px 4px 0;"><i class="fas fa-search"></i></button>
          </div>
        </div>
      </div>
      <div style="display: flex;">
        <button class="btn btn-primary" data-modal-target="createCanvasModal"><i class="fas fa-plus"></i> 创建画布</button>
      </div>
    </div>

    <!-- 画布列表 -->
    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 24px;">
      <!-- 销售分析画布 -->
      <div class="card" style="height: 300px; display: flex; flex-direction: column;">
        <div style="position: relative; height: 180px; overflow: hidden;">
          <img src="https://picsum.photos/id/1/800/400" alt="销售分析画布" style="width: 100%; height: 100%; object-fit: cover;">
          <div style="position: absolute; top: 8px; right: 8px;">
            <span class="badge success">已发布</span>
          </div>
        </div>
        <div style="padding: 16px;">
          <div style="font-size: 16px; font-weight: 500; margin-bottom: 8px;">销售数据分析画布</div>
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <div style="font-size: 12px; color: var(--text-tertiary);">创建人：张三</div>
            <div style="display: flex;">
              <button class="btn" style="color: var(--primary-color); margin-right: 8px;"><i class="fas fa-edit"></i> 编辑</button>
              <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash-alt"></i> 删除</button>
            </div>
          </div>
          <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 8px;">
            <div style="font-size: 12px; color: var(--text-tertiary);">更新时间：2023-09-25 14:30:00</div>
            <div style="font-size: 12px; color: var(--text-tertiary);"><i class="fas fa-user-friends"></i> 共享</div>
          </div>
        </div>
      </div>

      <!-- 运营监控画布 -->
      <div class="card" style="height: 300px; display: flex; flex-direction: column;">
        <div style="position: relative; height: 180px; overflow: hidden;">
          <img src="https://picsum.photos/id/20/800/400" alt="运营监控画布" style="width: 100%; height: 100%; object-fit: cover;">
          <div style="position: absolute; top: 8px; right: 8px;">
            <span class="badge success">已发布</span>
          </div>
        </div>
        <div style="padding: 16px;">
          <div style="font-size: 16px; font-weight: 500; margin-bottom: 8px;">运营监控总览画布</div>
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <div style="font-size: 12px; color: var(--text-tertiary);">创建人：李四</div>
            <div style="display: flex;">
              <button class="btn" style="color: var(--primary-color); margin-right: 8px;"><i class="fas fa-edit"></i> 编辑</button>
              <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash-alt"></i> 删除</button>
            </div>
          </div>
          <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 8px;">
            <div style="font-size: 12px; color: var(--text-tertiary);">更新时间：2023-09-26 09:15:00</div>
            <div style="font-size: 12px; color: var(--text-tertiary);"><i class="fas fa-user"></i> 个人</div>
          </div>
        </div>
      </div>

      <!-- 客户分析画布 -->
      <div class="card" style="height: 300px; display: flex; flex-direction: column;">
        <div style="position: relative; height: 180px; overflow: hidden;">
          <img src="https://picsum.photos/id/48/800/400" alt="客户分析画布" style="width: 100%; height: 100%; object-fit: cover;">
          <div style="position: absolute; top: 8px; right: 8px;">
            <span class="badge warning">草稿</span>
          </div>
        </div>
        <div style="padding: 16px;">
          <div style="font-size: 16px; font-weight: 500; margin-bottom: 8px;">客户画像分析画布</div>
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <div style="font-size: 12px; color: var(--text-tertiary);">创建人：王五</div>
            <div style="display: flex;">
              <button class="btn" style="color: var(--primary-color); margin-right: 8px;"><i class="fas fa-edit"></i> 编辑</button>
              <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash-alt"></i> 删除</button>
            </div>
          </div>
          <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 8px;">
            <div style="font-size: 12px; color: var(--text-tertiary);">更新时间：2023-09-27 16:45:00</div>
            <div style="font-size: 12px; color: var(--text-tertiary);"><i class="fas fa-user"></i> 个人</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div style="display: flex; justify-content: flex-end; align-items: center; margin-top: 20px;">
      <div style="font-size: 14px; color: var(--text-tertiary); margin-right: 16px;">共 8 条记录，当前第 1 页</div>
      <div style="display: flex;">
        <button class="btn" style="border: 1px solid var(--border-color); margin-right: 4px;" disabled><i class="fas fa-chevron-left"></i></button>
        <button class="btn btn-primary" style="margin-right: 4px;">1</button>
        <button class="btn" style="border: 1px solid var(--border-color); margin-right: 4px;">2</button>
        <button class="btn" style="border: 1px solid var(--border-color); margin-right: 4px;">3</button>
        <button class="btn" style="border: 1px solid var(--border-color);"><i class="fas fa-chevron-right"></i></button>
      </div>
    </div>
  </div>

  <script src="js/common.js"></script>
</body>
</html>