// 监控数据API服务
export interface MetricDataPoint {
  timestamp: string
  value: number
}

export interface SystemMetrics {
  cpu: MetricDataPoint[]
  memory: MetricDataPoint[]
  network: {
    inbound: MetricDataPoint[]
    outbound: MetricDataPoint[]
  }
  requests: {
    total: MetricDataPoint[]
    success: MetricDataPoint[]
    error: MetricDataPoint[]
  }
}

export interface SystemOverview {
  cpu: {
    current: number
    trend: number
  }
  memory: {
    current: number
    total: number
    trend: number
  }
  network: {
    inbound: number
    outbound: number
    trend: number
  }
  alerts: {
    active: number
    trend: number
  }
}

// API基础配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'https://api.devops-demo.com'

// 生成MOCK时间序列数据
const generateTimeSeriesData = (
  hours: number = 24,
  baseValue: number = 50,
  variance: number = 20,
  trend: number = 0
): MetricDataPoint[] => {
  const data: MetricDataPoint[] = []
  const now = new Date()
  
  for (let i = hours * 12; i >= 0; i--) {
    const timestamp = new Date(now.getTime() - i * 5 * 60 * 1000) // 5分钟间隔
    const trendValue = trend * (hours * 12 - i) / (hours * 12)
    const randomVariance = (Math.random() - 0.5) * variance
    const value = Math.max(0, Math.min(100, baseValue + trendValue + randomVariance))
    
    data.push({
      timestamp: timestamp.toISOString(),
      value: Math.round(value * 100) / 100,
    })
  }
  
  return data
}

// 生成网络数据 (MB/s)
const generateNetworkData = (hours: number = 24): MetricDataPoint[] => {
  const data: MetricDataPoint[] = []
  const now = new Date()
  
  for (let i = hours * 12; i >= 0; i--) {
    const timestamp = new Date(now.getTime() - i * 5 * 60 * 1000)
    const hour = timestamp.getHours()
    
    // 模拟业务高峰期
    let baseValue = 10
    if (hour >= 9 && hour <= 11) baseValue = 25 // 上午高峰
    if (hour >= 14 && hour <= 16) baseValue = 30 // 下午高峰
    if (hour >= 19 && hour <= 21) baseValue = 35 // 晚上高峰
    
    const randomVariance = (Math.random() - 0.5) * 10
    const value = Math.max(0, baseValue + randomVariance)
    
    data.push({
      timestamp: timestamp.toISOString(),
      value: Math.round(value * 100) / 100,
    })
  }
  
  return data
}

// 生成请求数据
const generateRequestData = (hours: number = 24): MetricDataPoint[] => {
  const data: MetricDataPoint[] = []
  const now = new Date()
  
  for (let i = hours * 12; i >= 0; i--) {
    const timestamp = new Date(now.getTime() - i * 5 * 60 * 1000)
    const hour = timestamp.getHours()
    
    // 模拟请求量变化
    let baseValue = 100
    if (hour >= 9 && hour <= 11) baseValue = 300
    if (hour >= 14 && hour <= 16) baseValue = 350
    if (hour >= 19 && hour <= 21) baseValue = 400
    if (hour >= 0 && hour <= 6) baseValue = 50
    
    const randomVariance = (Math.random() - 0.5) * 50
    const value = Math.max(0, baseValue + randomVariance)
    
    data.push({
      timestamp: timestamp.toISOString(),
      value: Math.round(value),
    })
  }
  
  return data
}

// MOCK数据生成器
export const mockMonitoringApi = {
  // 获取系统概览
  async getSystemOverview(): Promise<SystemOverview> {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 300))
    
    return {
      cpu: {
        current: 58.2,
        trend: 2.4,
      },
      memory: {
        current: 1536, // MB
        total: 2048,
        trend: -120,
      },
      network: {
        inbound: 21.3,
        outbound: 8.7,
        trend: 3.2,
      },
      alerts: {
        active: 3,
        trend: -1,
      },
    }
  },

  // 获取系统指标数据
  async getSystemMetrics(timeRange: string = '1h'): Promise<SystemMetrics> {
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const hours = timeRange === '5m' ? 0.1 : 
                  timeRange === '15m' ? 0.25 :
                  timeRange === '1h' ? 1 :
                  timeRange === '6h' ? 6 : 24
    
    return {
      cpu: generateTimeSeriesData(hours, 58, 15, 2),
      memory: generateTimeSeriesData(hours, 75, 10, -1),
      network: {
        inbound: generateNetworkData(hours),
        outbound: generateNetworkData(hours).map(point => ({
          ...point,
          value: point.value * 0.4, // 出站流量通常较小
        })),
      },
      requests: {
        total: generateRequestData(hours),
        success: generateRequestData(hours).map(point => ({
          ...point,
          value: Math.round(point.value * 0.95), // 95%成功率
        })),
        error: generateRequestData(hours).map(point => ({
          ...point,
          value: Math.round(point.value * 0.05), // 5%错误率
        })),
      },
    }
  },
}

// 真实API接口（待实现）
export const monitoringApi = {
  // 获取系统概览
  async getSystemOverview(): Promise<SystemOverview> {
    const response = await fetch(`${API_BASE_URL}/api/monitoring/overview`)
    if (!response.ok) {
      throw new Error('Failed to fetch system overview')
    }
    return response.json()
  },

  // 获取系统指标数据
  async getSystemMetrics(timeRange: string = '1h'): Promise<SystemMetrics> {
    const response = await fetch(`${API_BASE_URL}/api/monitoring/metrics?timeRange=${timeRange}`)
    if (!response.ok) {
      throw new Error('Failed to fetch system metrics')
    }
    return response.json()
  },

  // 获取告警列表
  async getAlerts(status?: 'active' | 'resolved'): Promise<any[]> {
    const params = new URLSearchParams()
    if (status) params.append('status', status)
    
    const response = await fetch(`${API_BASE_URL}/api/monitoring/alerts?${params}`)
    if (!response.ok) {
      throw new Error('Failed to fetch alerts')
    }
    return response.json()
  },

  // 获取日志
  async getLogs(params: {
    search?: string
    level?: string
    service?: string
    limit?: number
    offset?: number
  }): Promise<any[]> {
    const searchParams = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        searchParams.append(key, value.toString())
      }
    })
    
    const response = await fetch(`${API_BASE_URL}/api/monitoring/logs?${searchParams}`)
    if (!response.ok) {
      throw new Error('Failed to fetch logs')
    }
    return response.json()
  },
}

// 开发环境使用MOCK数据，生产环境使用真实API
export const useMonitoringApi = () => {
  const isDevelopment = import.meta.env.DEV
  return isDevelopment ? mockMonitoringApi : monitoringApi
}
