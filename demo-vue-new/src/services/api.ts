import axios from 'axios'
import type { 
  Pipeline, 
  Application, 
  Alert, 
  ServiceTopology,
  ApiResponse,
  PaginatedResponse 
} from '@/types'

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加认证token
    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    console.error('API Error:', error)
    return Promise.reject(error)
  }
)

// Pipeline API
export const pipelineApi = {
  // 获取流水线列表
  getList: (): Promise<ApiResponse<Pipeline[]>> => {
    return api.get('/pipelines')
  },

  // 获取流水线详情
  getById: (id: string): Promise<ApiResponse<Pipeline>> => {
    return api.get(`/pipelines/${id}`)
  },

  // 创建流水线
  create: (data: Partial<Pipeline>): Promise<ApiResponse<Pipeline>> => {
    return api.post('/pipelines', data)
  },

  // 运行流水线
  run: (id: string): Promise<ApiResponse<void>> => {
    return api.post(`/pipelines/${id}/run`)
  },

  // 停止流水线
  stop: (id: string): Promise<ApiResponse<void>> => {
    return api.post(`/pipelines/${id}/stop`)
  },

  // 删除流水线
  delete: (id: string): Promise<ApiResponse<void>> => {
    return api.delete(`/pipelines/${id}`)
  },
}

// Application API
export const applicationApi = {
  // 获取应用列表
  getList: (environmentId?: string): Promise<ApiResponse<Application[]>> => {
    const params = environmentId ? { environmentId } : {}
    return api.get('/applications', { params })
  },

  // 获取应用详情
  getById: (id: string): Promise<ApiResponse<Application>> => {
    return api.get(`/applications/${id}`)
  },

  // 部署应用
  deploy: (data: Partial<Application>): Promise<ApiResponse<Application>> => {
    return api.post('/applications', data)
  },

  // 扩容/缩容
  scale: (id: string, instances: number, reason: string): Promise<ApiResponse<void>> => {
    return api.post(`/applications/${id}/scale`, { instances, reason })
  },

  // 重启应用
  restart: (id: string): Promise<ApiResponse<void>> => {
    return api.post(`/applications/${id}/restart`)
  },

  // 删除应用
  delete: (id: string): Promise<ApiResponse<void>> => {
    return api.delete(`/applications/${id}`)
  },
}

// Monitoring API
export const monitoringApi = {
  // 获取告警列表
  getAlerts: (): Promise<ApiResponse<Alert[]>> => {
    return api.get('/monitoring/alerts')
  },

  // 获取指标数据
  getMetrics: (service: string, metric: string, timeRange: string): Promise<ApiResponse<any[]>> => {
    return api.get('/monitoring/metrics', {
      params: { service, metric, timeRange }
    })
  },

  // 解决告警
  resolveAlert: (id: string): Promise<ApiResponse<void>> => {
    return api.post(`/monitoring/alerts/${id}/resolve`)
  },
}

// Topology API
export const topologyApi = {
  // 获取服务拓扑
  getTopology: (): Promise<ApiResponse<ServiceTopology>> => {
    return api.get('/topology')
  },

  // 更新服务位置
  updateServicePosition: (id: string, position: { x: number; y: number }): Promise<ApiResponse<void>> => {
    return api.put(`/topology/services/${id}/position`, position)
  },
}

// Dashboard API
export const dashboardApi = {
  // 获取仪表板数据
  getStats: (): Promise<ApiResponse<any>> => {
    return api.get('/dashboard/stats')
  },

  // 获取部署趋势
  getDeploymentTrend: (timeRange: string): Promise<ApiResponse<any[]>> => {
    return api.get('/dashboard/deployment-trend', {
      params: { timeRange }
    })
  },

  // 获取资源使用情况
  getResourceUsage: (): Promise<ApiResponse<any[]>> => {
    return api.get('/dashboard/resource-usage')
  },
}

export default api
