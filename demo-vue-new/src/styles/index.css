@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-gray-200;
  }
  body {
    @apply bg-gray-50 text-gray-900;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .card {
    @apply bg-white rounded-lg shadow-md border border-gray-200;
  }
  
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200;
  }
  
  .btn-secondary {
    @apply bg-gray-100 hover:bg-gray-200 text-gray-800 font-medium py-2 px-4 rounded-md transition-colors duration-200;
  }
  
  .btn-success {
    @apply bg-success-600 hover:bg-success-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200;
  }
  
  .btn-warning {
    @apply bg-warning-500 hover:bg-warning-600 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200;
  }
  
  .btn-error {
    @apply bg-error-600 hover:bg-error-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200;
  }
  
  .status-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .status-success {
    @apply bg-success-100 text-success-800;
  }
  
  .status-error {
    @apply bg-error-100 text-error-800;
  }
  
  .status-warning {
    @apply bg-warning-100 text-warning-800;
  }
  
  .status-info {
    @apply bg-primary-100 text-primary-800;
  }
  
  .status-pending {
    @apply bg-gray-100 text-gray-800;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

.slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .7;
  }
}

.pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
