// 通用类型定义
export interface BaseEntity {
  id: string;
  createdAt: string;
  updatedAt: string;
}

// 状态类型
export type Status = 'pending' | 'running' | 'success' | 'failed' | 'cancelled';

// DevOps 流水线相关类型
export interface Pipeline extends BaseEntity {
  name: string;
  description: string;
  status: Status;
  stages: PipelineStage[];
  repository: Repository;
  branch: string;
  commitHash: string;
  commitMessage: string;
  author: string;
  duration: number;
  triggeredBy: string;
}

export interface PipelineStage {
  id: string;
  name: string;
  status: Status;
  startTime?: string;
  endTime?: string;
  duration?: number;
  logs?: string[];
  steps: PipelineStep[];
}

export interface PipelineStep {
  id: string;
  name: string;
  status: Status;
  command?: string;
  output?: string;
  error?: string;
  duration?: number;
}

export interface Repository {
  id: string;
  name: string;
  url: string;
  provider: 'gitlab' | 'github' | 'gitee';
}

// 容器化部署相关类型
export interface Application extends BaseEntity {
  name: string;
  description: string;
  status: ApplicationStatus;
  version: string;
  image: string;
  environment: Environment;
  instances: ServiceInstance[];
  healthCheck: HealthCheck;
  resources: ResourceUsage;
  deploymentStrategy: DeploymentStrategy;
}

export type ApplicationStatus = 'healthy' | 'unhealthy' | 'deploying' | 'scaling' | 'stopped';

export interface Environment {
  id: string;
  name: string;
  type: 'development' | 'testing' | 'staging' | 'production';
  description: string;
}

export interface ServiceInstance extends BaseEntity {
  name: string;
  status: 'running' | 'pending' | 'failed' | 'terminated';
  podName: string;
  nodeName: string;
  ip: string;
  port: number;
  cpu: number;
  memory: number;
  restartCount: number;
  uptime: string;
}

export interface HealthCheck {
  enabled: boolean;
  path: string;
  interval: number;
  timeout: number;
  threshold: number;
  status: 'healthy' | 'unhealthy' | 'unknown';
  lastCheck: string;
}

export interface ResourceUsage {
  cpu: {
    current: number;
    limit: number;
    unit: string;
  };
  memory: {
    current: number;
    limit: number;
    unit: string;
  };
  network: {
    inbound: number;
    outbound: number;
    unit: string;
  };
  storage: {
    used: number;
    total: number;
    unit: string;
  };
}

export interface DeploymentStrategy {
  type: 'rolling' | 'blue-green' | 'canary';
  maxUnavailable: number;
  maxSurge: number;
  canaryPercentage?: number;
}

// 服务拓扑相关类型
export interface ServiceTopology {
  services: ServiceNode[];
  connections: ServiceConnection[];
}

export interface ServiceNode {
  id: string;
  name: string;
  type: 'service' | 'database' | 'external' | 'gateway';
  status: ApplicationStatus;
  instances: number;
  position: {
    x: number;
    y: number;
  };
  metadata: Record<string, any>;
}

export interface ServiceConnection {
  id: string;
  source: string;
  target: string;
  type: 'http' | 'grpc' | 'tcp' | 'database';
  protocol: string;
  port: number;
  latency: number;
  throughput: number;
}

// 监控相关类型
export interface MetricData {
  timestamp: string;
  value: number;
  label?: string;
}

export interface Alert extends BaseEntity {
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'active' | 'resolved' | 'suppressed';
  source: string;
  tags: string[];
  resolvedAt?: string;
}

export interface LogEntry {
  id: string;
  timestamp: string;
  level: 'debug' | 'info' | 'warn' | 'error' | 'fatal';
  message: string;
  service: string;
  source: string;
  metadata: Record<string, any>;
}

// Docker 镜像相关类型
export interface DockerImage extends BaseEntity {
  name: string;
  tag: string;
  repository: string;
  size: number;
  architecture: string;
  os: string;
  digest: string;
  layers: ImageLayer[];
  vulnerabilities: Vulnerability[];
  labels: Record<string, string>;
}

export interface ImageLayer {
  id: string;
  command: string;
  size: number;
  createdAt: string;
}

export interface Vulnerability {
  id: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  package: string;
  version: string;
  fixedVersion?: string;
  description: string;
  cveId?: string;
}

// API 响应类型
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 扩容缩容相关类型
export interface ScalingOperation {
  id: string;
  applicationId: string;
  applicationName: string;
  action: 'scale-up' | 'scale-down';
  fromInstances: number;
  toInstances: number;
  status: Status;
  startTime: string;
  endTime?: string;
  duration?: number;
  reason: string;
  operator: string;
}

// 用户和权限类型
export interface User {
  id: string;
  username: string;
  email: string;
  role: 'admin' | 'developer' | 'operator' | 'viewer';
  avatar?: string;
  lastLogin: string;
}

// 配置类型
export interface SystemConfig {
  theme: 'light' | 'dark' | 'auto';
  language: 'zh-CN' | 'en-US';
  autoRefresh: boolean;
  refreshInterval: number;
  notifications: {
    alerts: boolean;
    deployments: boolean;
    scaling: boolean;
  };
}
