import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Pipeline, Application, Alert, User, SystemConfig } from '@/types'

// 用户状态管理
export const useUserStore = defineStore('user', () => {
  const user = ref<User | null>(null)
  const isAuthenticated = computed(() => !!user.value)

  const setUser = (userData: User) => {
    user.value = userData
  }

  const logout = () => {
    user.value = null
    localStorage.removeItem('auth_token')
  }

  return {
    user,
    isAuthenticated,
    setUser,
    logout,
  }
})

// 流水线状态管理
export const usePipelineStore = defineStore('pipeline', () => {
  const pipelines = ref<Pipeline[]>([])
  const selectedPipeline = ref<Pipeline | null>(null)
  const loading = ref(false)

  const setPipelines = (data: Pipeline[]) => {
    pipelines.value = data
  }

  const addPipeline = (pipeline: Pipeline) => {
    pipelines.value.push(pipeline)
  }

  const updatePipeline = (id: string, updates: Partial<Pipeline>) => {
    const index = pipelines.value.findIndex(p => p.id === id)
    if (index !== -1) {
      pipelines.value[index] = { ...pipelines.value[index], ...updates }
    }
  }

  const removePipeline = (id: string) => {
    const index = pipelines.value.findIndex(p => p.id === id)
    if (index !== -1) {
      pipelines.value.splice(index, 1)
    }
  }

  const setSelectedPipeline = (pipeline: Pipeline | null) => {
    selectedPipeline.value = pipeline
  }

  return {
    pipelines,
    selectedPipeline,
    loading,
    setPipelines,
    addPipeline,
    updatePipeline,
    removePipeline,
    setSelectedPipeline,
  }
})

// 应用状态管理
export const useApplicationStore = defineStore('application', () => {
  const applications = ref<Application[]>([])
  const selectedApplication = ref<Application | null>(null)
  const loading = ref(false)

  const setApplications = (data: Application[]) => {
    applications.value = data
  }

  const addApplication = (application: Application) => {
    applications.value.push(application)
  }

  const updateApplication = (id: string, updates: Partial<Application>) => {
    const index = applications.value.findIndex(a => a.id === id)
    if (index !== -1) {
      applications.value[index] = { ...applications.value[index], ...updates }
    }
  }

  const removeApplication = (id: string) => {
    const index = applications.value.findIndex(a => a.id === id)
    if (index !== -1) {
      applications.value.splice(index, 1)
    }
  }

  const setSelectedApplication = (application: Application | null) => {
    selectedApplication.value = application
  }

  return {
    applications,
    selectedApplication,
    loading,
    setApplications,
    addApplication,
    updateApplication,
    removeApplication,
    setSelectedApplication,
  }
})

// 监控状态管理
export const useMonitoringStore = defineStore('monitoring', () => {
  const alerts = ref<Alert[]>([])
  const metrics = ref<Record<string, any[]>>({})
  const loading = ref(false)

  const setAlerts = (data: Alert[]) => {
    alerts.value = data
  }

  const addAlert = (alert: Alert) => {
    alerts.value.unshift(alert)
  }

  const resolveAlert = (id: string) => {
    const index = alerts.value.findIndex(a => a.id === id)
    if (index !== -1) {
      alerts.value[index].status = 'resolved'
      alerts.value[index].resolvedAt = new Date().toISOString()
    }
  }

  const setMetrics = (key: string, data: any[]) => {
    metrics.value[key] = data
  }

  const activeAlerts = computed(() => 
    alerts.value.filter(alert => alert.status === 'active')
  )

  const criticalAlerts = computed(() =>
    activeAlerts.value.filter(alert => alert.severity === 'critical')
  )

  return {
    alerts,
    metrics,
    loading,
    activeAlerts,
    criticalAlerts,
    setAlerts,
    addAlert,
    resolveAlert,
    setMetrics,
  }
})

// 系统配置状态管理
export const useConfigStore = defineStore('config', () => {
  const config = ref<SystemConfig>({
    theme: 'light',
    language: 'zh-CN',
    autoRefresh: true,
    refreshInterval: 30,
    notifications: {
      alerts: true,
      deployments: true,
      scaling: true,
    },
  })

  const updateConfig = (updates: Partial<SystemConfig>) => {
    config.value = { ...config.value, ...updates }
    // 保存到localStorage
    localStorage.setItem('system_config', JSON.stringify(config.value))
  }

  const loadConfig = () => {
    const saved = localStorage.getItem('system_config')
    if (saved) {
      try {
        config.value = { ...config.value, ...JSON.parse(saved) }
      } catch (error) {
        console.error('Failed to load config:', error)
      }
    }
  }

  return {
    config,
    updateConfig,
    loadConfig,
  }
})

// 全局状态管理
export const useGlobalStore = defineStore('global', () => {
  const loading = ref(false)
  const error = ref<string | null>(null)
  const sidebarCollapsed = ref(false)

  const setLoading = (value: boolean) => {
    loading.value = value
  }

  const setError = (message: string | null) => {
    error.value = message
  }

  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }

  return {
    loading,
    error,
    sidebarCollapsed,
    setLoading,
    setError,
    toggleSidebar,
  }
})
