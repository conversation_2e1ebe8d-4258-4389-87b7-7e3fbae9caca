<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">容器部署管理</h1>
        <p class="text-gray-600">微服务容器化部署与扩缩容管理</p>
      </div>
      <button
        @click="router.push('/deployment/create')"
        class="btn-primary flex items-center space-x-2"
      >
        <Container class="h-4 w-4" />
        <span>部署新应用</span>
      </button>
    </div>

    <!-- 环境选择 -->
    <div class="card p-4">
      <div class="flex items-center space-x-4">
        <span class="text-sm font-medium text-gray-700">环境:</span>
        <div class="flex space-x-2">
          <button
            v-for="env in environments"
            :key="env.id"
            @click="selectedEnvironment = env"
            :class="[
              'px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200',
              selectedEnvironment.id === env.id
                ? 'bg-primary-100 text-primary-700 border border-primary-200'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            ]"
          >
            {{ env.name }}
          </button>
        </div>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
      <div class="card p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600">应用总数</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats.totalApps }}</p>
          </div>
          <Container class="h-8 w-8 text-primary-500" />
        </div>
      </div>
      <div class="card p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600">健康应用</p>
            <p class="text-2xl font-bold text-green-600">{{ stats.healthyApps }}</p>
          </div>
          <Activity class="h-8 w-8 text-green-500" />
        </div>
      </div>
      <div class="card p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600">异常应用</p>
            <p class="text-2xl font-bold text-red-600">{{ stats.unhealthyApps }}</p>
          </div>
          <AlertTriangle class="h-8 w-8 text-red-500" />
        </div>
      </div>
      <div class="card p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600">运行实例</p>
            <p class="text-2xl font-bold text-blue-600">
              {{ stats.runningInstances }}/{{ stats.totalInstances }}
            </p>
          </div>
          <Cpu class="h-8 w-8 text-blue-500" />
        </div>
      </div>
    </div>

    <!-- 应用列表 -->
    <div class="space-y-4">
      <h3 class="text-lg font-semibold text-gray-900">
        {{ selectedEnvironment.name }} - 应用列表
      </h3>
      <div v-for="app in filteredApplications" :key="app.id">
        <ApplicationCard
          :application="app"
          @scale="handleScaleApplication"
        />
      </div>
    </div>

    <!-- 扩缩容对话框 -->
    <ScalingDialog
      :is-open="isScalingDialogOpen"
      :application="scalingApp"
      @close="handleCloseScalingDialog"
      @confirm="handleConfirmScaling"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import {
  Container,
  Activity,
  AlertTriangle,
  Cpu,
} from 'lucide-vue-next'
import ApplicationCard from '@/components/deployment/ApplicationCard.vue'
import ScalingDialog from '@/components/deployment/ScalingDialog.vue'
import type { Application, Environment } from '@/types'

const router = useRouter()
const scalingApp = ref<Application | null>(null)
const isScalingDialogOpen = ref(false)

// 模拟环境数据
const environments: Environment[] = [
  {
    id: 'dev',
    name: '开发环境',
    type: 'development',
    description: '开发人员日常开发使用',
  },
  {
    id: 'test',
    name: '测试环境',
    type: 'testing',
    description: 'QA团队测试使用',
  },
  {
    id: 'staging',
    name: '预发环境',
    type: 'staging',
    description: '生产前最后验证',
  },
  {
    id: 'prod',
    name: '生产环境',
    type: 'production',
    description: '正式对外服务',
  },
]

const selectedEnvironment = ref<Environment>(environments[3])

// 模拟应用数据
const mockApplications: Application[] = [
  {
    id: '1',
    name: 'user-service',
    description: '用户管理服务',
    status: 'healthy',
    version: 'v1.2.0',
    image: 'registry.company.com/user-service:v1.2.0',
    environment: environments[3], // 生产环境
    instances: [
      {
        id: 'user-service-1',
        name: 'user-service-deployment-7d4b9c8f6d-abc12',
        status: 'running',
        podName: 'user-service-deployment-7d4b9c8f6d-abc12',
        nodeName: 'node-1',
        ip: '************',
        port: 8080,
        cpu: 0.5,
        memory: 512,
        restartCount: 0,
        uptime: '2天3小时',
        createdAt: '2024-01-13T07:00:00Z',
        updatedAt: '2024-01-13T07:00:00Z',
      },
      {
        id: 'user-service-2',
        name: 'user-service-deployment-7d4b9c8f6d-def34',
        status: 'running',
        podName: 'user-service-deployment-7d4b9c8f6d-def34',
        nodeName: 'node-2',
        ip: '************',
        port: 8080,
        cpu: 0.4,
        memory: 480,
        restartCount: 0,
        uptime: '2天3小时',
        createdAt: '2024-01-13T07:00:00Z',
        updatedAt: '2024-01-13T07:00:00Z',
      },
      {
        id: 'user-service-3',
        name: 'user-service-deployment-7d4b9c8f6d-ghi56',
        status: 'running',
        podName: 'user-service-deployment-7d4b9c8f6d-ghi56',
        nodeName: 'node-1',
        ip: '************',
        port: 8080,
        cpu: 0.6,
        memory: 520,
        restartCount: 1,
        uptime: '1天5小时',
        createdAt: '2024-01-14T05:00:00Z',
        updatedAt: '2024-01-14T05:00:00Z',
      },
    ],
    healthCheck: {
      enabled: true,
      path: '/health',
      interval: 30,
      timeout: 5,
      threshold: 3,
      status: 'healthy',
      lastCheck: '2024-01-15T10:00:00Z',
    },
    resources: {
      cpu: {
        current: 1.5,
        limit: 2.0,
        unit: 'cores',
      },
      memory: {
        current: 1512,
        limit: 2048,
        unit: 'MB',
      },
      network: {
        inbound: 10.5,
        outbound: 5.2,
        unit: 'MB/s',
      },
      storage: {
        used: 2.1,
        total: 10.0,
        unit: 'GB',
      },
    },
    deploymentStrategy: {
      type: 'rolling',
      maxUnavailable: 1,
      maxSurge: 1,
    },
    createdAt: '2024-01-13T07:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
  },
  {
    id: '2',
    name: 'order-service',
    description: '订单管理服务',
    status: 'scaling',
    version: 'v2.1.3',
    image: 'registry.company.com/order-service:v2.1.3',
    environment: environments[3],
    instances: [
      {
        id: 'order-service-1',
        name: 'order-service-deployment-8e5c9d7f6e-xyz78',
        status: 'running',
        podName: 'order-service-deployment-8e5c9d7f6e-xyz78',
        nodeName: 'node-2',
        ip: '************',
        port: 8080,
        cpu: 0.8,
        memory: 768,
        restartCount: 0,
        uptime: '1天2小时',
        createdAt: '2024-01-14T08:00:00Z',
        updatedAt: '2024-01-14T08:00:00Z',
      },
      {
        id: 'order-service-2',
        name: 'order-service-deployment-8e5c9d7f6e-uvw90',
        status: 'pending',
        podName: 'order-service-deployment-8e5c9d7f6e-uvw90',
        nodeName: 'node-1',
        ip: '************',
        port: 8080,
        cpu: 0,
        memory: 0,
        restartCount: 0,
        uptime: '启动中',
        createdAt: '2024-01-15T10:00:00Z',
        updatedAt: '2024-01-15T10:00:00Z',
      },
    ],
    healthCheck: {
      enabled: true,
      path: '/actuator/health',
      interval: 30,
      timeout: 10,
      threshold: 3,
      status: 'healthy',
      lastCheck: '2024-01-15T10:00:00Z',
    },
    resources: {
      cpu: {
        current: 0.8,
        limit: 2.0,
        unit: 'cores',
      },
      memory: {
        current: 768,
        limit: 1024,
        unit: 'MB',
      },
      network: {
        inbound: 15.2,
        outbound: 8.7,
        unit: 'MB/s',
      },
      storage: {
        used: 1.5,
        total: 5.0,
        unit: 'GB',
      },
    },
    deploymentStrategy: {
      type: 'blue-green',
      maxUnavailable: 0,
      maxSurge: 2,
    },
    createdAt: '2024-01-14T08:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
  },
  {
    id: '3',
    name: 'payment-service',
    description: '支付处理服务',
    status: 'unhealthy',
    version: 'v1.5.2',
    image: 'registry.company.com/payment-service:v1.5.2',
    environment: environments[3],
    instances: [
      {
        id: 'payment-service-1',
        name: 'payment-service-deployment-9f6d8e7c5b-mno34',
        status: 'failed',
        podName: 'payment-service-deployment-9f6d8e7c5b-mno34',
        nodeName: 'node-3',
        ip: '************',
        port: 8080,
        cpu: 0,
        memory: 0,
        restartCount: 5,
        uptime: '失败',
        createdAt: '2024-01-15T09:00:00Z',
        updatedAt: '2024-01-15T10:00:00Z',
      },
    ],
    healthCheck: {
      enabled: true,
      path: '/health',
      interval: 30,
      timeout: 5,
      threshold: 3,
      status: 'unhealthy',
      lastCheck: '2024-01-15T10:00:00Z',
    },
    resources: {
      cpu: {
        current: 0,
        limit: 1.0,
        unit: 'cores',
      },
      memory: {
        current: 0,
        limit: 512,
        unit: 'MB',
      },
      network: {
        inbound: 0,
        outbound: 0,
        unit: 'MB/s',
      },
      storage: {
        used: 0.8,
        total: 2.0,
        unit: 'GB',
      },
    },
    deploymentStrategy: {
      type: 'rolling',
      maxUnavailable: 1,
      maxSurge: 1,
    },
    createdAt: '2024-01-15T09:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
  },
]

const filteredApplications = computed(() => {
  return mockApplications.filter(app => app.environment.id === selectedEnvironment.value.id)
})

// 统计数据
const stats = computed(() => {
  const apps = filteredApplications.value
  return {
    totalApps: apps.length,
    healthyApps: apps.filter(app => app.status === 'healthy').length,
    unhealthyApps: apps.filter(app => app.status === 'unhealthy').length,
    scalingApps: apps.filter(app => app.status === 'scaling').length,
    totalInstances: apps.reduce((sum, app) => sum + app.instances.length, 0),
    runningInstances: apps.reduce(
      (sum, app) => sum + app.instances.filter(instance => instance.status === 'running').length,
      0
    ),
  }
})

const handleScaleApplication = (app: Application) => {
  scalingApp.value = app
  isScalingDialogOpen.value = true
}

const handleCloseScalingDialog = () => {
  isScalingDialogOpen.value = false
  scalingApp.value = null
}

const handleConfirmScaling = (targetInstances: number, reason: string) => {
  if (!scalingApp.value) return

  console.log('扩缩容操作:', {
    application: scalingApp.value.name,
    from: scalingApp.value.instances.length,
    to: targetInstances,
    reason,
  })

  // 实际应用中会调用API
  handleCloseScalingDialog()
}
</script>
