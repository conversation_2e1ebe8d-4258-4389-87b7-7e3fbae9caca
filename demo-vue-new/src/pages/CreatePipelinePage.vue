<template>
  <div class="max-w-4xl mx-auto space-y-6">
    <!-- 页面头部 -->
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <button
          @click="router.push('/pipeline')"
          class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md"
        >
          <ArrowLeft class="h-5 w-5" />
        </button>
        <div>
          <h1 class="text-2xl font-bold text-gray-900">新建流水线</h1>
          <p class="text-gray-600">配置CI/CD流水线自动化流程</p>
        </div>
      </div>

      <div class="flex items-center space-x-3">
        <button
          type="button"
          @click="router.push('/pipeline')"
          class="btn-secondary"
        >
          取消
        </button>
        <button
          type="submit"
          form="pipeline-form"
          :disabled="isSubmitting"
          class="btn-primary flex items-center space-x-2"
        >
          <div v-if="isSubmitting" class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          <Save v-else class="h-4 w-4" />
          <span>{{ isSubmitting ? '创建中...' : '创建流水线' }}</span>
        </button>
      </div>
    </div>

    <!-- 标签页导航 -->
    <div class="border-b border-gray-200">
      <nav class="-mb-px flex space-x-8">
        <button
          v-for="tab in tabs"
          :key="tab.id"
          @click="activeTab = tab.id"
          :class="[
            'py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2',
            activeTab === tab.id
              ? 'border-primary-500 text-primary-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          ]"
        >
          <component :is="tab.icon" class="h-4 w-4" />
          <span>{{ tab.name }}</span>
        </button>
      </nav>
    </div>

    <!-- 表单内容 -->
    <form id="pipeline-form" @submit="handleSubmit" class="space-y-8">
      <!-- 基本信息 -->
      <div v-if="activeTab === 'basic'" class="card p-6 space-y-6">
        <h3 class="text-lg font-semibold text-gray-900">基本信息</h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              流水线名称 <span class="text-red-500">*</span>
            </label>
            <input
              type="text"
              v-model="form.name"
              placeholder="例如: user-service-pipeline"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              required
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              部署环境
            </label>
            <select
              v-model="form.environment"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="development">开发环境</option>
              <option value="testing">测试环境</option>
              <option value="staging">预发环境</option>
              <option value="production">生产环境</option>
            </select>
          </div>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            描述
          </label>
          <textarea
            v-model="form.description"
            placeholder="流水线功能描述..."
            rows="3"
            class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
        </div>

        <div class="space-y-4">
          <h4 class="text-md font-medium text-gray-900">代码仓库</h4>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                仓库地址 <span class="text-red-500">*</span>
              </label>
              <input
                type="url"
                v-model="form.repository.url"
                placeholder="https://github.com/company/repo"
                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                required
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                分支
              </label>
              <input
                type="text"
                v-model="form.repository.branch"
                placeholder="main"
                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                代码托管平台
              </label>
              <select
                v-model="form.repository.provider"
                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                <option value="github">GitHub</option>
                <option value="gitlab">GitLab</option>
                <option value="gitee">Gitee</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      <!-- 流水线阶段 -->
      <div v-if="activeTab === 'stages'" class="card p-6 space-y-6">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-900">流水线阶段</h3>
          <button
            type="button"
            @click="addStage"
            class="btn-primary flex items-center space-x-2"
          >
            <Plus class="h-4 w-4" />
            <span>添加阶段</span>
          </button>
        </div>

        <div class="space-y-4">
          <div v-for="(stage, index) in form.stages" :key="stage.id" class="border border-gray-200 rounded-lg p-4">
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center space-x-3">
                <div class="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center text-sm font-medium">
                  {{ index + 1 }}
                </div>
                <input
                  type="text"
                  v-model="stage.name"
                  class="border-none bg-transparent text-lg font-medium focus:outline-none focus:ring-2 focus:ring-primary-500 rounded px-2 py-1"
                />
              </div>

              <div class="flex items-center space-x-2">
                <label class="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    v-model="stage.enabled"
                    class="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <span class="text-sm text-gray-600">启用</span>
                </label>

                <div class="flex items-center space-x-1">
                  <button
                    type="button"
                    @click="moveStage(stage.id, 'up')"
                    :disabled="index === 0"
                    class="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"
                  >
                    ↑
                  </button>
                  <button
                    type="button"
                    @click="moveStage(stage.id, 'down')"
                    :disabled="index === form.stages.length - 1"
                    class="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"
                  >
                    ↓
                  </button>
                  <button
                    type="button"
                    @click="removeStage(stage.id)"
                    class="p-1 text-red-400 hover:text-red-600"
                  >
                    <Trash2 class="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>

            <div class="space-y-3">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  阶段类型
                </label>
                <select
                  :value="stage.type"
                  @change="updateStageType(stage.id, ($event.target as HTMLSelectElement).value)"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="checkout">代码检出</option>
                  <option value="build">构建</option>
                  <option value="test">测试</option>
                  <option value="docker">Docker构建</option>
                  <option value="deploy">部署</option>
                  <option value="custom">自定义</option>
                </select>
              </div>

              <!-- 根据阶段类型显示不同的配置选项 -->
              <div v-if="stage.type === 'build'" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    构建命令
                  </label>
                  <input
                    type="text"
                    :value="stage.config.command || ''"
                    @input="updateStageConfig(stage.id, 'command', ($event.target as HTMLInputElement).value)"
                    placeholder="npm run build"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    输出目录
                  </label>
                  <input
                    type="text"
                    :value="stage.config.output || ''"
                    @input="updateStageConfig(stage.id, 'output', ($event.target as HTMLInputElement).value)"
                    placeholder="dist"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                </div>
              </div>

              <div v-if="stage.type === 'docker'" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    Dockerfile路径
                  </label>
                  <input
                    type="text"
                    :value="stage.config.dockerfile || ''"
                    @input="updateStageConfig(stage.id, 'dockerfile', ($event.target as HTMLInputElement).value)"
                    placeholder="Dockerfile"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    镜像名称
                  </label>
                  <input
                    type="text"
                    :value="stage.config.image || ''"
                    @input="updateStageConfig(stage.id, 'image', ($event.target as HTMLInputElement).value)"
                    placeholder="app:latest"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                </div>
              </div>

              <div v-if="stage.type === 'deploy'" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    部署策略
                  </label>
                  <select
                    :value="stage.config.strategy || 'rolling'"
                    @change="updateStageConfig(stage.id, 'strategy', ($event.target as HTMLSelectElement).value)"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  >
                    <option value="rolling">滚动更新</option>
                    <option value="blue-green">蓝绿部署</option>
                    <option value="canary">金丝雀发布</option>
                  </select>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    目标环境
                  </label>
                  <select
                    :value="stage.config.environment || 'development'"
                    @change="updateStageConfig(stage.id, 'environment', ($event.target as HTMLSelectElement).value)"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  >
                    <option value="development">开发环境</option>
                    <option value="testing">测试环境</option>
                    <option value="staging">预发环境</option>
                    <option value="production">生产环境</option>
                  </select>
                </div>
              </div>

              <div v-if="stage.type === 'test'" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    测试命令
                  </label>
                  <input
                    type="text"
                    :value="stage.config.command || ''"
                    @input="updateStageConfig(stage.id, 'command', ($event.target as HTMLInputElement).value)"
                    placeholder="npm test"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    覆盖率阈值 (%)
                  </label>
                  <input
                    type="number"
                    min="0"
                    max="100"
                    :value="stage.config.coverage || ''"
                    @input="updateStageConfig(stage.id, 'coverage', ($event.target as HTMLInputElement).value)"
                    placeholder="80"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                </div>
              </div>

              <div v-if="stage.type === 'custom'" class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    自定义命令
                  </label>
                  <textarea
                    :value="stage.config.script || ''"
                    @input="updateStageConfig(stage.id, 'script', ($event.target as HTMLTextAreaElement).value)"
                    placeholder="#!/bin/bash&#10;echo 'Custom script here'"
                    rows="4"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent font-mono text-sm"
                  />
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                      工作目录
                    </label>
                    <input
                      type="text"
                      :value="stage.config.workingDirectory || ''"
                      @input="updateStageConfig(stage.id, 'workingDirectory', ($event.target as HTMLInputElement).value)"
                      placeholder="./"
                      class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                      超时时间 (分钟)
                    </label>
                    <input
                      type="number"
                      min="1"
                      max="60"
                      :value="stage.config.timeout || ''"
                      @input="updateStageConfig(stage.id, 'timeout', ($event.target as HTMLInputElement).value)"
                      placeholder="10"
                      class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 触发条件 -->
      <div v-if="activeTab === 'triggers'" class="card p-6 space-y-6">
        <h3 class="text-lg font-semibold text-gray-900">触发条件</h3>

        <div class="space-y-4">
          <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
            <div>
              <h4 class="font-medium text-gray-900">代码推送</h4>
              <p class="text-sm text-gray-600">当代码推送到指定分支时触发流水线</p>
            </div>
            <label class="flex items-center">
              <input
                type="checkbox"
                v-model="form.triggers.push"
                class="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              />
            </label>
          </div>

          <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
            <div>
              <h4 class="font-medium text-gray-900">Pull Request</h4>
              <p class="text-sm text-gray-600">当创建或更新Pull Request时触发流水线</p>
            </div>
            <label class="flex items-center">
              <input
                type="checkbox"
                v-model="form.triggers.pullRequest"
                class="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              />
            </label>
          </div>

          <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
            <div>
              <h4 class="font-medium text-gray-900">手动触发</h4>
              <p class="text-sm text-gray-600">允许用户手动触发流水线</p>
            </div>
            <label class="flex items-center">
              <input
                type="checkbox"
                v-model="form.triggers.manual"
                class="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              />
            </label>
          </div>

          <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
            <div>
              <h4 class="font-medium text-gray-900">定时触发</h4>
              <p class="text-sm text-gray-600">按计划定时触发流水线</p>
            </div>
            <label class="flex items-center">
              <input
                type="checkbox"
                v-model="form.triggers.schedule"
                class="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              />
            </label>
          </div>

          <div v-if="form.triggers.schedule" class="p-4 bg-gray-50 rounded-lg">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Cron表达式
            </label>
            <input
              type="text"
              v-model="form.triggers.scheduleCron"
              placeholder="0 0 * * * (每天午夜)"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
            <p class="text-xs text-gray-500 mt-1">
              使用标准Cron表达式格式，例如：0 0 * * * 表示每天午夜执行
            </p>
          </div>
        </div>
      </div>

      <!-- 高级设置 -->
      <div v-if="activeTab === 'advanced'" class="card p-6 space-y-6">
        <h3 class="text-lg font-semibold text-gray-900">高级设置</h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              超时时间 (分钟)
            </label>
            <input
              type="number"
              min="1"
              max="120"
              v-model="form.timeout"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              并行执行
            </label>
            <select
              v-model="form.parallel"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="sequential">顺序执行</option>
              <option value="parallel">并行执行</option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              失败策略
            </label>
            <select
              v-model="form.failureStrategy"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="fail-fast">快速失败</option>
              <option value="continue">继续执行</option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              通知设置
            </label>
            <select
              v-model="form.notification"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="all">所有事件</option>
              <option value="failure">仅失败</option>
              <option value="success">仅成功</option>
              <option value="none">不通知</option>
            </select>
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useToast } from 'vue-toastification'
import {
  ArrowLeft,
  Save,
  Settings,
  GitBranch,
  Zap,
  Sliders,
  Plus,
  Trash2,
} from 'lucide-vue-next'
import { generateId } from '@/utils'

const router = useRouter()
const toast = useToast()

// 标签页配置
const tabs = [
  { id: 'basic', name: '基本信息', icon: Settings },
  { id: 'stages', name: '流水线阶段', icon: GitBranch },
  { id: 'triggers', name: '触发条件', icon: Zap },
  { id: 'advanced', name: '高级设置', icon: Sliders },
]

const activeTab = ref('basic')
const isSubmitting = ref(false)

// 表单数据
const form = reactive({
  name: '',
  description: '',
  environment: 'development',
  repository: {
    url: '',
    branch: 'main',
    provider: 'github' as 'github' | 'gitlab' | 'gitee',
  },
  stages: [
    {
      id: generateId(),
      name: '代码检出',
      type: 'checkout',
      enabled: true,
      config: {},
    },
    {
      id: generateId(),
      name: '构建',
      type: 'build',
      enabled: true,
      config: {
        command: 'npm run build',
        output: 'dist',
      },
    },
  ],
  triggers: {
    push: true,
    pullRequest: false,
    manual: true,
    schedule: false,
    scheduleCron: '',
  },
  timeout: 30,
  parallel: 'sequential' as 'sequential' | 'parallel',
  failureStrategy: 'fail-fast' as 'fail-fast' | 'continue',
  notification: 'all' as 'all' | 'failure' | 'success' | 'none',
})

// 添加阶段
const addStage = () => {
  const newStage = {
    id: generateId(),
    name: '新阶段',
    type: 'custom',
    enabled: true,
    config: {},
  }
  form.stages.push(newStage)
}

// 移除阶段
const removeStage = (stageId: string) => {
  const index = form.stages.findIndex(stage => stage.id === stageId)
  if (index !== -1) {
    form.stages.splice(index, 1)
  }
}

// 移动阶段
const moveStage = (stageId: string, direction: 'up' | 'down') => {
  const index = form.stages.findIndex(stage => stage.id === stageId)
  if (index === -1) return

  const newIndex = direction === 'up' ? index - 1 : index + 1
  if (newIndex < 0 || newIndex >= form.stages.length) return

  const stage = form.stages.splice(index, 1)[0]
  form.stages.splice(newIndex, 0, stage)
}

// 更新阶段配置
const updateStageConfig = (stageId: string, key: string, value: any) => {
  const stage = form.stages.find(s => s.id === stageId)
  if (stage) {
    if (!stage.config) {
      stage.config = {}
    }
    stage.config[key] = value
  }
}

// 更新阶段类型
const updateStageType = (stageId: string, type: string) => {
  const stage = form.stages.find(s => s.id === stageId)
  if (stage) {
    stage.type = type as any
    // 根据类型初始化默认配置
    switch (type) {
      case 'checkout':
        stage.config = { branch: 'main' }
        break
      case 'build':
        stage.config = { command: 'npm run build', output: 'dist' }
        break
      case 'test':
        stage.config = { command: 'npm test', coverage: '80' }
        break
      case 'docker':
        stage.config = { dockerfile: 'Dockerfile', image: 'app:latest' }
        break
      case 'deploy':
        stage.config = { strategy: 'rolling', environment: 'development' }
        break
      case 'custom':
        stage.config = { script: '', workingDirectory: './', timeout: '10' }
        break
      default:
        stage.config = {}
    }
  }
}

// 表单提交
const handleSubmit = async (event: Event) => {
  event.preventDefault()

  if (!form.name.trim()) {
    toast.error('请输入流水线名称')
    return
  }

  if (!form.repository.url.trim()) {
    toast.error('请输入仓库地址')
    return
  }

  isSubmitting.value = true

  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))

    console.log('创建流水线:', form)

    toast.success('流水线创建成功！')
    router.push('/pipeline')
  } catch (error) {
    console.error('创建流水线失败:', error)
    toast.error('创建流水线失败，请重试')
  } finally {
    isSubmitting.value = false
  }
}
</script>
