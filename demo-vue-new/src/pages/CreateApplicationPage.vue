<template>
  <div class="max-w-4xl mx-auto space-y-6">
    <!-- 页面头部 -->
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <button
          @click="router.push('/deployment')"
          class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md"
        >
          <ArrowLeft class="h-5 w-5" />
        </button>
        <div>
          <h1 class="text-2xl font-bold text-gray-900">部署新应用</h1>
          <p class="text-gray-600">配置和部署新的微服务应用</p>
        </div>
      </div>

      <div class="flex items-center space-x-3">
        <button
          type="button"
          @click="router.push('/deployment')"
          class="btn-secondary"
        >
          取消
        </button>
        <button
          type="submit"
          form="application-form"
          :disabled="isSubmitting"
          class="btn-primary flex items-center space-x-2"
        >
          <div v-if="isSubmitting" class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          <Save v-else class="h-4 w-4" />
          <span>{{ isSubmitting ? '部署中...' : '部署应用' }}</span>
        </button>
      </div>
    </div>

    <!-- 标签页导航 -->
    <div class="border-b border-gray-200">
      <nav class="-mb-px flex space-x-8">
        <button
          v-for="tab in tabs"
          :key="tab.id"
          @click="activeTab = tab.id"
          :class="[
            'py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2',
            activeTab === tab.id
              ? 'border-primary-500 text-primary-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          ]"
        >
          <component :is="tab.icon" class="h-4 w-4" />
          <span>{{ tab.name }}</span>
        </button>
      </nav>
    </div>

    <!-- 表单内容 -->
    <form id="application-form" @submit="handleSubmit" class="space-y-8">
      <!-- 基本信息 -->
      <div v-if="activeTab === 'basic'" class="card p-6 space-y-6">
        <h3 class="text-lg font-semibold text-gray-900">基本信息</h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              应用名称 <span class="text-red-500">*</span>
            </label>
            <input
              type="text"
              v-model="form.name"
              placeholder="例如: user-service"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              required
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              部署环境
            </label>
            <select
              v-model="form.environment"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="development">开发环境</option>
              <option value="testing">测试环境</option>
              <option value="staging">预发环境</option>
              <option value="production">生产环境</option>
            </select>
          </div>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            描述
          </label>
          <textarea
            v-model="form.description"
            placeholder="应用功能描述..."
            rows="3"
            class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
        </div>

        <div class="space-y-4">
          <h4 class="text-md font-medium text-gray-900">容器镜像</h4>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                镜像仓库 <span class="text-red-500">*</span>
              </label>
              <input
                type="text"
                v-model="form.image.repository"
                placeholder="user-service"
                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                required
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                镜像标签
              </label>
              <input
                type="text"
                v-model="form.image.tag"
                placeholder="latest"
                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                镜像仓库地址
              </label>
              <input
                type="text"
                v-model="form.image.registry"
                placeholder="registry.company.com"
                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              副本数量
            </label>
            <input
              type="number"
              min="1"
              max="20"
              v-model.number="form.replicas"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              部署策略
            </label>
            <select
              v-model="form.deploymentStrategy.type"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="rolling">滚动更新</option>
              <option value="blue-green">蓝绿部署</option>
              <option value="canary">金丝雀发布</option>
            </select>
          </div>
        </div>
      </div>

      <!-- 资源配置 -->
      <div v-if="activeTab === 'resources'" class="card p-6 space-y-6">
        <h3 class="text-lg font-semibold text-gray-900">资源配置</h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 class="text-md font-medium text-gray-900 mb-4">CPU 配置</h4>
            <div class="space-y-3">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  请求值
                </label>
                <input
                  type="text"
                  v-model="form.resources.cpu.request"
                  placeholder="100m"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  限制值
                </label>
                <input
                  type="text"
                  v-model="form.resources.cpu.limit"
                  placeholder="500m"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          <div>
            <h4 class="text-md font-medium text-gray-900 mb-4">内存配置</h4>
            <div class="space-y-3">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  请求值
                </label>
                <input
                  type="text"
                  v-model="form.resources.memory.request"
                  placeholder="128Mi"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  限制值
                </label>
                <input
                  type="text"
                  v-model="form.resources.memory.limit"
                  placeholder="512Mi"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>
        </div>

        <div class="border-t border-gray-200 pt-6">
          <h4 class="text-md font-medium text-gray-900 mb-4">自动扩缩容</h4>
          <div class="space-y-4">
            <label class="flex items-center space-x-2">
              <input
                type="checkbox"
                v-model="form.autoscaling.enabled"
                class="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              />
              <span class="text-sm font-medium text-gray-700">启用自动扩缩容</span>
            </label>

            <div v-if="form.autoscaling.enabled" class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  最小副本数
                </label>
                <input
                  type="number"
                  min="1"
                  v-model.number="form.autoscaling.minReplicas"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  最大副本数
                </label>
                <input
                  type="number"
                  min="1"
                  v-model.number="form.autoscaling.maxReplicas"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  CPU目标使用率 (%)
                </label>
                <input
                  type="number"
                  min="1"
                  max="100"
                  v-model.number="form.autoscaling.targetCPUUtilizationPercentage"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  内存目标使用率 (%)
                </label>
                <input
                  type="number"
                  min="1"
                  max="100"
                  v-model.number="form.autoscaling.targetMemoryUtilizationPercentage"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 网络配置 -->
      <div v-if="activeTab === 'network'" class="card p-6 space-y-6">
        <h3 class="text-lg font-semibold text-gray-900">网络配置</h3>

        <div class="space-y-6">
          <div>
            <h4 class="text-md font-medium text-gray-900 mb-4">服务端口</h4>
            <div class="space-y-3">
              <div
                v-for="(port, index) in form.ports"
                :key="index"
                class="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg"
              >
                <div class="flex-1 grid grid-cols-1 md:grid-cols-4 gap-3">
                  <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">
                      端口名称
                    </label>
                    <input
                      type="text"
                      v-model="port.name"
                      placeholder="http"
                      class="w-full border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-primary-500"
                    />
                  </div>
                  <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">
                      端口号
                    </label>
                    <input
                      type="number"
                      min="1"
                      max="65535"
                      v-model.number="port.port"
                      placeholder="8080"
                      class="w-full border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-primary-500"
                    />
                  </div>
                  <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">
                      目标端口
                    </label>
                    <input
                      type="number"
                      min="1"
                      max="65535"
                      v-model.number="port.targetPort"
                      placeholder="8080"
                      class="w-full border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-primary-500"
                    />
                  </div>
                  <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">
                      协议
                    </label>
                    <select
                      v-model="port.protocol"
                      class="w-full border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-primary-500"
                    >
                      <option value="TCP">TCP</option>
                      <option value="UDP">UDP</option>
                    </select>
                  </div>
                </div>
                <button
                  type="button"
                  @click="removePort(index)"
                  class="p-1 text-red-400 hover:text-red-600"
                >
                  <Trash2 class="h-4 w-4" />
                </button>
              </div>

              <button
                type="button"
                @click="addPort"
                class="btn-secondary flex items-center space-x-2"
              >
                <Plus class="h-4 w-4" />
                <span>添加端口</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 健康检查 -->
      <div v-if="activeTab === 'health'" class="card p-6 space-y-6">
        <h3 class="text-lg font-semibold text-gray-900">健康检查</h3>

        <div class="space-y-6">
          <!-- 存活探针 -->
          <div>
            <div class="flex items-center justify-between mb-4">
              <h4 class="text-md font-medium text-gray-900">存活探针 (Liveness Probe)</h4>
              <label class="flex items-center space-x-2">
                <input
                  type="checkbox"
                  v-model="form.healthCheck.enabled"
                  class="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span class="text-sm text-gray-600">启用</span>
              </label>
            </div>

            <div v-if="form.healthCheck.enabled" class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  检查类型
                </label>
                <select
                  v-model="form.healthCheck.type"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="http">HTTP GET</option>
                  <option value="tcp">TCP Socket</option>
                  <option value="exec">执行命令</option>
                </select>
              </div>

              <div v-if="form.healthCheck.type === 'http'" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    路径
                  </label>
                  <input
                    type="text"
                    v-model="form.healthCheck.httpGet.path"
                    placeholder="/health"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    端口
                  </label>
                  <input
                    type="number"
                    min="1"
                    max="65535"
                    v-model.number="form.healthCheck.httpGet.port"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    协议
                  </label>
                  <select
                    v-model="form.healthCheck.httpGet.scheme"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  >
                    <option value="HTTP">HTTP</option>
                    <option value="HTTPS">HTTPS</option>
                  </select>
                </div>
              </div>

              <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    初始延迟 (秒)
                  </label>
                  <input
                    type="number"
                    min="0"
                    v-model.number="form.healthCheck.initialDelaySeconds"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    检查间隔 (秒)
                  </label>
                  <input
                    type="number"
                    min="1"
                    v-model.number="form.healthCheck.periodSeconds"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    超时时间 (秒)
                  </label>
                  <input
                    type="number"
                    min="1"
                    v-model.number="form.healthCheck.timeoutSeconds"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    成功阈值
                  </label>
                  <input
                    type="number"
                    min="1"
                    v-model.number="form.healthCheck.successThreshold"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    失败阈值
                  </label>
                  <input
                    type="number"
                    min="1"
                    v-model.number="form.healthCheck.failureThreshold"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- 就绪探针 -->
          <div class="border-t border-gray-200 pt-6">
            <div class="flex items-center justify-between mb-4">
              <h4 class="text-md font-medium text-gray-900">就绪探针 (Readiness Probe)</h4>
              <label class="flex items-center space-x-2">
                <input
                  type="checkbox"
                  v-model="form.readinessProbe.enabled"
                  class="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span class="text-sm text-gray-600">启用</span>
              </label>
            </div>

            <div v-if="form.readinessProbe.enabled" class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  检查类型
                </label>
                <select
                  v-model="form.readinessProbe.type"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="http">HTTP GET</option>
                  <option value="tcp">TCP Socket</option>
                  <option value="exec">执行命令</option>
                </select>
              </div>

              <div v-if="form.readinessProbe.type === 'http'" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    路径
                  </label>
                  <input
                    type="text"
                    v-model="form.readinessProbe.httpGet.path"
                    placeholder="/ready"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    端口
                  </label>
                  <input
                    type="number"
                    min="1"
                    max="65535"
                    v-model.number="form.readinessProbe.httpGet.port"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    协议
                  </label>
                  <select
                    v-model="form.readinessProbe.httpGet.scheme"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  >
                    <option value="HTTP">HTTP</option>
                    <option value="HTTPS">HTTPS</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 安全配置 -->
      <div v-if="activeTab === 'security'" class="card p-6 space-y-6">
        <h3 class="text-lg font-semibold text-gray-900">安全配置</h3>

        <div class="space-y-6">
          <div>
            <h4 class="text-md font-medium text-gray-900 mb-4">安全上下文</h4>
            <div class="space-y-4">
              <label class="flex items-center space-x-2">
                <input
                  type="checkbox"
                  v-model="form.security.runAsNonRoot"
                  class="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span class="text-sm font-medium text-gray-700">以非root用户运行</span>
              </label>

              <label class="flex items-center space-x-2">
                <input
                  type="checkbox"
                  v-model="form.security.readOnlyRootFilesystem"
                  class="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span class="text-sm font-medium text-gray-700">只读根文件系统</span>
              </label>

              <label class="flex items-center space-x-2">
                <input
                  type="checkbox"
                  v-model="form.security.allowPrivilegeEscalation"
                  class="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span class="text-sm font-medium text-gray-700">允许权限提升</span>
              </label>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  运行用户ID
                </label>
                <input
                  type="number"
                  min="0"
                  v-model.number="form.security.runAsUser"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useToast } from 'vue-toastification'
import {
  ArrowLeft,
  Save,
  Settings,
  Cpu,
  Network,
  Shield,
  Activity,
  Plus,
  Trash2,
} from 'lucide-vue-next'
import { generateId } from '@/utils'

const router = useRouter()
const toast = useToast()

// 标签页配置
const tabs = [
  { id: 'basic', name: '基本信息', icon: Settings },
  { id: 'resources', name: '资源配置', icon: Cpu },
  { id: 'network', name: '网络配置', icon: Network },
  { id: 'health', name: '健康检查', icon: Activity },
  { id: 'security', name: '安全配置', icon: Shield },
]

const activeTab = ref('basic')
const isSubmitting = ref(false)

// 表单数据
const form = reactive({
  name: '',
  description: '',
  environment: 'development',
  image: {
    repository: '',
    tag: 'latest',
    registry: 'registry.company.com',
  },
  replicas: 1,
  deploymentStrategy: {
    type: 'rolling' as 'rolling' | 'blue-green' | 'canary',
    maxUnavailable: 1,
    maxSurge: 1,
  },
  resources: {
    cpu: {
      request: '100m',
      limit: '500m',
    },
    memory: {
      request: '128Mi',
      limit: '512Mi',
    },
  },
  autoscaling: {
    enabled: false,
    minReplicas: 1,
    maxReplicas: 10,
    targetCPUUtilizationPercentage: 70,
    targetMemoryUtilizationPercentage: 80,
  },
  ports: [
    {
      id: generateId(),
      name: 'http',
      port: 8080,
      targetPort: 8080,
      protocol: 'TCP' as 'TCP' | 'UDP',
    },
  ],
  healthCheck: {
    enabled: true,
    type: 'http' as 'http' | 'tcp' | 'exec',
    httpGet: {
      path: '/health',
      port: 8080,
      scheme: 'HTTP' as 'HTTP' | 'HTTPS',
    },
    initialDelaySeconds: 30,
    periodSeconds: 10,
    timeoutSeconds: 5,
    successThreshold: 1,
    failureThreshold: 3,
  },
  readinessProbe: {
    enabled: true,
    type: 'http' as 'http' | 'tcp' | 'exec',
    httpGet: {
      path: '/ready',
      port: 8080,
      scheme: 'HTTP' as 'HTTP' | 'HTTPS',
    },
    initialDelaySeconds: 5,
    periodSeconds: 5,
    timeoutSeconds: 3,
    successThreshold: 1,
    failureThreshold: 3,
  },
  security: {
    runAsNonRoot: true,
    runAsUser: 1000,
    readOnlyRootFilesystem: false,
    allowPrivilegeEscalation: false,
    capabilities: {
      drop: ['ALL'],
      add: [] as string[],
    },
  },
  env: [] as Array<{
    id: string
    name: string
    value: string
    type: 'value' | 'secret' | 'configmap'
  }>,
  volumes: [] as Array<{
    id: string
    name: string
    type: 'emptyDir' | 'configMap' | 'secret' | 'persistentVolumeClaim'
    mountPath: string
    source: string
  }>,
})

// 添加端口
const addPort = () => {
  form.ports.push({
    id: generateId(),
    name: '',
    port: 8080,
    targetPort: 8080,
    protocol: 'TCP',
  })
}

// 移除端口
const removePort = (index: number) => {
  if (form.ports.length > 1) {
    form.ports.splice(index, 1)
  }
}

// 表单提交
const handleSubmit = async (event: Event) => {
  event.preventDefault()

  if (!form.name.trim()) {
    toast.error('请输入应用名称')
    return
  }

  if (!form.image.repository.trim()) {
    toast.error('请输入镜像仓库')
    return
  }

  isSubmitting.value = true

  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))

    console.log('部署应用:', form)

    toast.success('应用部署成功！')
    router.push('/deployment')
  } catch (error) {
    console.error('部署应用失败:', error)
    toast.error('部署应用失败，请重试')
  } finally {
    isSubmitting.value = false
  }
}
</script>
