<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">服务拓扑</h1>
        <p class="text-gray-600">微服务架构拓扑图和依赖关系</p>
      </div>
      <div class="flex items-center space-x-2">
        <button class="btn-secondary">
          <RefreshCw class="h-4 w-4 mr-2" />
          刷新
        </button>
        <button class="btn-secondary">
          <ZoomIn class="h-4 w-4 mr-2" />
          放大
        </button>
      </div>
    </div>

    <!-- 拓扑图容器 -->
    <div class="card p-6">
      <div class="h-[600px] bg-gray-50 rounded-lg relative overflow-hidden">
        <!-- 网格背景 -->
        <div class="absolute inset-0 opacity-20">
          <svg class="w-full h-full">
            <defs>
              <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#e5e7eb" stroke-width="1"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#grid)" />
          </svg>
        </div>

        <!-- 服务节点 -->
        <ServiceNode
          v-for="service in services"
          :key="service.id"
          :node="service"
          :is-selected="selectedService?.id === service.id"
          @select="selectedService = $event"
        />

        <!-- 连接线 -->
        <svg class="absolute inset-0 w-full h-full pointer-events-none">
          <defs>
            <marker id="arrowhead" markerWidth="10" markerHeight="7"
             refX="9" refY="3.5" orient="auto">
              <polygon points="0 0, 10 3.5, 0 7" fill="#6B7280" />
            </marker>
            <marker id="arrowhead-success" markerWidth="10" markerHeight="7"
             refX="9" refY="3.5" orient="auto">
              <polygon points="0 0, 10 3.5, 0 7" fill="#10B981" />
            </marker>
            <marker id="arrowhead-error" markerWidth="10" markerHeight="7"
             refX="9" refY="3.5" orient="auto">
              <polygon points="0 0, 10 3.5, 0 7" fill="#EF4444" />
            </marker>
          </defs>

          <!-- API Gateway 到 User Service -->
          <line
            x1="260" y1="120" x2="260" y2="180"
            stroke="#10B981" stroke-width="2" marker-end="url(#arrowhead-success)"
          />

          <!-- API Gateway 到 Order Service -->
          <line
            x1="260" y1="120" x2="460" y2="180"
            stroke="#10B981" stroke-width="2" marker-end="url(#arrowhead-success)"
          />

          <!-- User Service 到 Database -->
          <line
            x1="260" y1="240" x2="260" y2="300"
            stroke="#10B981" stroke-width="2" marker-end="url(#arrowhead-success)"
          />

          <!-- Order Service 到 Database -->
          <line
            x1="460" y1="240" x2="340" y2="300"
            stroke="#EF4444" stroke-width="2" marker-end="url(#arrowhead-error)"
          />

          <!-- Order Service 到 Payment Service -->
          <line
            x1="460" y1="240" x2="660" y2="240"
            stroke="#6B7280" stroke-width="2" marker-end="url(#arrowhead)"
          />
        </svg>

        <!-- 图例 -->
        <div class="absolute top-4 right-4 bg-white rounded-lg shadow-md p-4 border">
          <h4 class="text-sm font-medium text-gray-900 mb-2">连接状态</h4>
          <div class="space-y-1 text-xs">
            <div class="flex items-center space-x-2">
              <div class="w-4 h-0.5 bg-green-500"></div>
              <span class="text-gray-600">正常连接</span>
            </div>
            <div class="flex items-center space-x-2">
              <div class="w-4 h-0.5 bg-red-500"></div>
              <span class="text-gray-600">异常连接</span>
            </div>
            <div class="flex items-center space-x-2">
              <div class="w-4 h-0.5 bg-gray-500"></div>
              <span class="text-gray-600">未知状态</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 服务列表 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div v-for="service in services" :key="service.id" class="card p-6">
        <div class="flex items-start justify-between mb-4">
          <div>
            <h3 class="text-lg font-semibold text-gray-900">{{ service.name }}</h3>
            <p class="text-sm text-gray-600">{{ service.type }}</p>
          </div>
          <span :class="['status-badge', getStatusClass(service.status)]">
            {{ getStatusText(service.status) }}
          </span>
        </div>

        <div class="space-y-2">
          <div class="flex justify-between text-sm">
            <span class="text-gray-500">实例数:</span>
            <span class="font-medium">{{ service.instances }}</span>
          </div>
          <div class="flex justify-between text-sm">
            <span class="text-gray-500">位置:</span>
            <span class="font-medium">{{ service.position.x }}, {{ service.position.y }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { RefreshCw, ZoomIn, Network } from 'lucide-vue-next'
import ServiceNode from '@/components/topology/ServiceNode.vue'
import type { ServiceNode as ServiceNodeType, ApplicationStatus } from '@/types'

const selectedService = ref<ServiceNodeType | null>(null)

const services: ServiceNodeType[] = [
  {
    id: '1',
    name: 'API Gateway',
    type: 'gateway',
    status: 'healthy',
    instances: 2,
    position: { x: 180, y: 50 },
    metadata: {
      version: 'v1.2.0',
      port: 8080,
      cpu: '0.5 cores',
      memory: '512MB',
    },
  },
  {
    id: '2',
    name: 'User Service',
    type: 'service',
    status: 'healthy',
    instances: 3,
    position: { x: 180, y: 200 },
    metadata: {
      version: 'v2.1.0',
      port: 8081,
      cpu: '1.0 cores',
      memory: '1GB',
    },
  },
  {
    id: '3',
    name: 'Order Service',
    type: 'service',
    status: 'unhealthy',
    instances: 2,
    position: { x: 380, y: 200 },
    metadata: {
      version: 'v1.8.3',
      port: 8082,
      cpu: '0.8 cores',
      memory: '768MB',
    },
  },
  {
    id: '4',
    name: 'Payment Service',
    type: 'service',
    status: 'healthy',
    instances: 2,
    position: { x: 580, y: 200 },
    metadata: {
      version: 'v1.5.2',
      port: 8083,
      cpu: '0.6 cores',
      memory: '512MB',
    },
  },
  {
    id: '5',
    name: 'MySQL Database',
    type: 'database',
    status: 'healthy',
    instances: 1,
    position: { x: 180, y: 350 },
    metadata: {
      version: '8.0.32',
      port: 3306,
      storage: '100GB',
      connections: 150,
    },
  },
  {
    id: '6',
    name: 'Redis Cache',
    type: 'database',
    status: 'healthy',
    instances: 1,
    position: { x: 380, y: 350 },
    metadata: {
      version: '7.0.8',
      port: 6379,
      memory: '2GB',
      keys: 50000,
    },
  },
]

const getStatusClass = (status: ApplicationStatus) => {
  const statusMap = {
    healthy: 'status-success',
    unhealthy: 'status-error',
    deploying: 'status-info',
    scaling: 'status-warning',
    stopped: 'status-pending',
  }
  return statusMap[status]
}

const getStatusText = (status: ApplicationStatus) => {
  const statusMap = {
    healthy: '健康',
    unhealthy: '异常',
    deploying: '部署中',
    scaling: '扩容中',
    stopped: '已停止',
  }
  return statusMap[status]
}
</script>
