<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">系统总览</h1>
        <p class="text-gray-600">DevOps 平台运行状态监控</p>
      </div>
      <div class="text-sm text-gray-500">
        最后更新: {{ new Date().toLocaleString('zh-CN') }}
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div v-for="(stat, index) in dashboardStats" :key="index" class="card p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">{{ stat.title }}</p>
            <p class="text-2xl font-bold text-gray-900">{{ stat.value }}</p>
            <div class="flex items-center mt-2">
              <span
                :class="[
                  'text-sm font-medium',
                  stat.changeType === 'increase'
                    ? 'text-success-600'
                    : 'text-error-600'
                ]"
              >
                {{ stat.change }}
              </span>
              <span class="text-sm text-gray-500 ml-1">vs 昨日</span>
            </div>
          </div>
          <div :class="['p-3 rounded-lg', stat.color]">
            <component :is="stat.icon" class="h-6 w-6 text-white" />
          </div>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 部署趋势图 -->
      <div class="card p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">部署趋势</h3>
          <TrendingUp class="h-5 w-5 text-gray-400" />
        </div>
        <v-chart 
          class="chart" 
          :option="deploymentTrendOption" 
          style="height: 250px;"
        />
      </div>

      <!-- 资源使用情况 -->
      <div class="card p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">集群资源使用率</h3>
          <Activity class="h-5 w-5 text-gray-400" />
        </div>
        <v-chart 
          class="chart" 
          :option="resourceUsageOption" 
          style="height: 250px;"
        />
        <div class="grid grid-cols-2 gap-4 mt-4">
          <div v-for="(item, index) in resourceUsage" :key="index" class="flex items-center space-x-2">
            <div
              class="w-3 h-3 rounded-full"
              :style="{ backgroundColor: item.color }"
            ></div>
            <span class="text-sm text-gray-600">{{ item.name }}</span>
            <span class="text-sm font-medium text-gray-900">
              {{ item.value }}%
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近的流水线 -->
    <div class="card">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-900">最近的流水线</h3>
          <button class="text-primary-600 hover:text-primary-700 text-sm font-medium">
            查看全部
          </button>
        </div>
      </div>
      <div class="divide-y divide-gray-200">
        <div v-for="pipeline in recentPipelines" :key="pipeline.id" class="px-6 py-4 flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="flex-shrink-0">
              <CheckCircle v-if="pipeline.status === 'success'" class="h-5 w-5 text-success-500" />
              <XCircle v-else-if="pipeline.status === 'failed'" class="h-5 w-5 text-error-500" />
              <div v-else-if="pipeline.status === 'running'" class="h-5 w-5 rounded-full border-2 border-primary-500 border-t-transparent animate-spin"></div>
            </div>
            <div>
              <div class="text-sm font-medium text-gray-900">
                {{ pipeline.name }}
              </div>
              <div class="text-sm text-gray-500">
                分支: {{ pipeline.branch }}
              </div>
            </div>
          </div>
          <div class="flex items-center space-x-4 text-sm text-gray-500">
            <div class="flex items-center space-x-1">
              <Clock class="h-4 w-4" />
              <span>{{ pipeline.duration }}</span>
            </div>
            <span>{{ pipeline.time }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import VChart from 'vue-echarts'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, PieChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import {
  Activity,
  Container,
  GitBranch,
  AlertTriangle,
  TrendingUp,
  Clock,
  CheckCircle,
  XCircle,
} from 'lucide-vue-next'

use([
  CanvasRenderer,
  LineChart,
  PieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

// 模拟数据
const dashboardStats = [
  {
    title: '运行中的应用',
    value: '24',
    change: '+2',
    changeType: 'increase' as const,
    icon: Container,
    color: 'bg-primary-500',
  },
  {
    title: '今日部署次数',
    value: '18',
    change: '+5',
    changeType: 'increase' as const,
    icon: GitBranch,
    color: 'bg-success-500',
  },
  {
    title: '活跃告警',
    value: '3',
    change: '-2',
    changeType: 'decrease' as const,
    icon: AlertTriangle,
    color: 'bg-warning-500',
  },
  {
    title: '系统健康度',
    value: '98.5%',
    change: '+0.2%',
    changeType: 'increase' as const,
    icon: Activity,
    color: 'bg-success-500',
  },
]

const deploymentTrend = [
  { time: '00:00', deployments: 2, success: 2 },
  { time: '04:00', deployments: 1, success: 1 },
  { time: '08:00', deployments: 5, success: 4 },
  { time: '12:00', deployments: 8, success: 7 },
  { time: '16:00', deployments: 12, success: 11 },
  { time: '20:00', deployments: 6, success: 6 },
]

const resourceUsage = [
  { name: 'CPU', value: 65, color: '#3B82F6' },
  { name: '内存', value: 78, color: '#10B981' },
  { name: '存储', value: 45, color: '#F59E0B' },
  { name: '网络', value: 32, color: '#EF4444' },
]

const recentPipelines = [
  {
    id: '1',
    name: 'user-service',
    status: 'success' as const,
    duration: '2m 34s',
    time: '5分钟前',
    branch: 'main',
  },
  {
    id: '2',
    name: 'order-service',
    status: 'running' as const,
    duration: '1m 45s',
    time: '正在进行',
    branch: 'develop',
  },
  {
    id: '3',
    name: 'payment-service',
    status: 'failed' as const,
    duration: '3m 12s',
    time: '15分钟前',
    branch: 'main',
  },
  {
    id: '4',
    name: 'notification-service',
    status: 'success' as const,
    duration: '1m 58s',
    time: '30分钟前',
    branch: 'main',
  },
]

// ECharts 配置
const deploymentTrendOption = computed(() => ({
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    data: ['总部署次数', '成功部署']
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: deploymentTrend.map(item => item.time)
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '总部署次数',
      type: 'line',
      data: deploymentTrend.map(item => item.deployments),
      itemStyle: { color: '#3B82F6' },
      lineStyle: { color: '#3B82F6' }
    },
    {
      name: '成功部署',
      type: 'line',
      data: deploymentTrend.map(item => item.success),
      itemStyle: { color: '#10B981' },
      lineStyle: { color: '#10B981' }
    }
  ]
}))

const resourceUsageOption = computed(() => ({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c}%'
  },
  series: [
    {
      name: '资源使用率',
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['50%', '50%'],
      data: resourceUsage.map(item => ({
        value: item.value,
        name: item.name,
        itemStyle: { color: item.color }
      }))
    }
  ]
}))
</script>

<style scoped>
.chart {
  width: 100%;
}
</style>
