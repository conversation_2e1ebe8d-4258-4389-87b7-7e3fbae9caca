<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">监控中心</h1>
        <p class="text-gray-600">系统性能监控与日志管理</p>
      </div>
      <div class="flex items-center space-x-2">
        <select
          v-model="selectedTimeRange"
          class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
        >
          <option value="5m">5分钟</option>
          <option value="15m">15分钟</option>
          <option value="1h">1小时</option>
          <option value="6h">6小时</option>
          <option value="24h">24小时</option>
        </select>
        <button
          @click="refreshAllCharts"
          class="btn-secondary flex items-center space-x-1 text-sm"
        >
          <RefreshCw class="h-4 w-4" />
          <span>刷新</span>
        </button>
      </div>
    </div>

    <!-- 系统概览 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div class="card p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">平均CPU使用率</p>
            <p class="text-2xl font-bold text-gray-900">58.2%</p>
            <div class="flex items-center mt-2">
              <TrendingUp class="h-4 w-4 text-green-500 mr-1" />
              <span class="text-sm text-green-600">+2.4%</span>
            </div>
          </div>
          <Cpu class="h-8 w-8 text-blue-500" />
        </div>
      </div>

      <div class="card p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">平均内存使用</p>
            <p class="text-2xl font-bold text-gray-900">1.5GB</p>
            <div class="flex items-center mt-2">
              <TrendingDown class="h-4 w-4 text-red-500 mr-1" />
              <span class="text-sm text-red-600">-120MB</span>
            </div>
          </div>
          <HardDrive class="h-8 w-8 text-green-500" />
        </div>
      </div>

      <div class="card p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">网络吞吐量</p>
            <p class="text-2xl font-bold text-gray-900">21.3MB/s</p>
            <div class="flex items-center mt-2">
              <TrendingUp class="h-4 w-4 text-green-500 mr-1" />
              <span class="text-sm text-green-600">+3.2MB/s</span>
            </div>
          </div>
          <Network class="h-8 w-8 text-purple-500" />
        </div>
      </div>

      <div class="card p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">活跃告警</p>
            <p class="text-2xl font-bold text-gray-900">
              {{ activeAlertsCount }}
            </p>
            <div class="flex items-center mt-2">
              <AlertTriangle class="h-4 w-4 text-orange-500 mr-1" />
              <span class="text-sm text-orange-600">需关注</span>
            </div>
          </div>
          <AlertTriangle class="h-8 w-8 text-orange-500" />
        </div>
      </div>
    </div>

    <!-- 性能图表 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- CPU使用率 -->
      <div class="card p-6">
        <CpuChart
          :time-range="selectedTimeRange"
          :auto-refresh="true"
          :refresh-interval="30000"
          ref="cpuChartRef"
        />
      </div>

      <!-- 内存使用 -->
      <div class="card p-6">
        <MemoryChart
          :time-range="selectedTimeRange"
          :auto-refresh="true"
          :refresh-interval="30000"
          ref="memoryChartRef"
        />
      </div>
    </div>

    <!-- 网络和请求统计 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 网络流量 -->
      <div class="card p-6">
        <NetworkChart
          :time-range="selectedTimeRange"
          :auto-refresh="true"
          :refresh-interval="30000"
          ref="networkChartRef"
        />
      </div>

      <!-- 请求统计 -->
      <div class="card p-6">
        <RequestChart
          :time-range="selectedTimeRange"
          :auto-refresh="true"
          :refresh-interval="30000"
          ref="requestChartRef"
        />
      </div>
    </div>

    <!-- 告警列表 -->
    <div class="card">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">系统告警</h3>
      </div>
      <div class="divide-y divide-gray-200">
        <div v-for="alert in alerts" :key="alert.id" class="px-6 py-4">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <div class="flex items-center space-x-2 mb-1">
                <span :class="['inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border', getSeverityColor(alert.severity)]">
                  {{ getSeverityText(alert.severity) }}
                </span>
                <span :class="['inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium', getStatusColor(alert.status)]">
                  {{ alert.status === 'active' ? '活跃' : '已解决' }}
                </span>
              </div>
              <h4 class="font-medium text-gray-900">{{ alert.title }}</h4>
              <p class="text-sm text-gray-600 mt-1">{{ alert.description }}</p>
              <div class="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                <span>来源: {{ alert.source }}</span>
                <span>创建: {{ formatDate(alert.createdAt) }}</span>
                <span v-if="alert.resolvedAt">解决: {{ formatDate(alert.resolvedAt) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 日志管理 -->
    <div class="card">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-900">系统日志</h3>
          <button class="btn-secondary flex items-center space-x-1 text-sm">
            <Download class="h-4 w-4" />
            <span>导出日志</span>
          </button>
        </div>

        <!-- 日志过滤 -->
        <div class="mt-4 flex flex-col sm:flex-row gap-4">
          <div class="flex-1">
            <div class="relative">
              <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="搜索日志..."
                v-model="logSearch"
                class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>
          </div>
          <select
            v-model="logLevel"
            class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="all">所有级别</option>
            <option value="error">错误</option>
            <option value="warn">警告</option>
            <option value="info">信息</option>
            <option value="debug">调试</option>
          </select>
          <select
            v-model="logService"
            class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="all">所有服务</option>
            <option value="user-service">user-service</option>
            <option value="order-service">order-service</option>
            <option value="payment-service">payment-service</option>
          </select>
        </div>
      </div>

      <!-- 日志列表 -->
      <div class="divide-y divide-gray-200 max-h-96 overflow-y-auto">
        <div v-for="log in filteredLogs" :key="log.id" class="px-6 py-3 hover:bg-gray-50">
          <div class="flex items-start space-x-3">
            <div class="flex-shrink-0">
              <span :class="['inline-flex items-center px-2 py-1 rounded text-xs font-medium', getLevelColor(log.level)]">
                {{ log.level.toUpperCase() }}
              </span>
            </div>
            <div class="flex-1 min-w-0">
              <div class="flex items-center space-x-2 text-sm text-gray-500">
                <span>{{ formatDate(log.timestamp) }}</span>
                <span>•</span>
                <span>{{ log.service }}</span>
                <span>•</span>
                <span>{{ log.source }}</span>
              </div>
              <p class="mt-1 text-sm text-gray-900">{{ log.message }}</p>
              <div v-if="Object.keys(log.metadata).length > 0" class="mt-1 text-xs text-gray-600">
                <span v-for="[key, value] in Object.entries(log.metadata)" :key="key" class="mr-3">
                  {{ key }}: {{ typeof value === 'object' ? JSON.stringify(value) : value }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import {
  RefreshCw,
  AlertTriangle,
  Cpu,
  HardDrive,
  Activity,
  Network,
  TrendingUp,
  TrendingDown,
  Database,
  Download,
  Search,
} from 'lucide-vue-next'
import CpuChart from '@/components/charts/CpuChart.vue'
import MemoryChart from '@/components/charts/MemoryChart.vue'
import NetworkChart from '@/components/charts/NetworkChart.vue'
import RequestChart from '@/components/charts/RequestChart.vue'
import { useMonitoringApi } from '@/services/monitoringApi'
import type { Alert, LogEntry } from '@/types'

// API服务
const monitoringApi = useMonitoringApi()

// 响应式状态
const selectedTimeRange = ref('1h')
const logSearch = ref('')
const logLevel = ref('all')
const logService = ref('all')

// 图表组件引用
const cpuChartRef = ref()
const memoryChartRef = ref()
const networkChartRef = ref()
const requestChartRef = ref()

// 告警数据
const alerts: Alert[] = [
  {
    id: '1',
    title: 'CPU使用率过高',
    description: 'user-service CPU使用率超过80%，可能影响服务性能',
    severity: 'high',
    source: 'user-service',
    status: 'active',
    createdAt: '2024-01-15T10:30:00Z',
  },
  {
    id: '2',
    title: '内存泄漏检测',
    description: 'order-service 内存使用持续增长，建议重启服务',
    severity: 'medium',
    source: 'order-service',
    status: 'active',
    createdAt: '2024-01-15T09:15:00Z',
  },
  {
    id: '3',
    title: '数据库连接异常',
    description: '数据库连接池耗尽，已自动恢复',
    severity: 'critical',
    source: 'database',
    status: 'resolved',
    createdAt: '2024-01-15T08:00:00Z',
    resolvedAt: '2024-01-15T08:30:00Z',
  },
  {
    id: '4',
    title: '磁盘空间不足',
    description: '/var/log 目录磁盘使用率超过90%',
    severity: 'high',
    source: 'system',
    status: 'active',
    createdAt: '2024-01-15T07:45:00Z',
  },
]

// 日志数据
const logs: LogEntry[] = [
  {
    id: '1',
    timestamp: '2024-01-15T10:35:00Z',
    level: 'error',
    service: 'user-service',
    source: 'UserController',
    message: 'Failed to authenticate user: invalid token',
    metadata: {
      userId: '12345',
      endpoint: '/api/users/profile',
      statusCode: 401,
    },
  },
  {
    id: '2',
    timestamp: '2024-01-15T10:34:30Z',
    level: 'warn',
    service: 'order-service',
    source: 'OrderProcessor',
    message: 'Order processing took longer than expected',
    metadata: {
      orderId: 'ORD-67890',
      processingTime: '5.2s',
      threshold: '3s',
    },
  },
  {
    id: '3',
    timestamp: '2024-01-15T10:34:15Z',
    level: 'info',
    service: 'payment-service',
    source: 'PaymentGateway',
    message: 'Payment processed successfully',
    metadata: {
      transactionId: 'TXN-11111',
      amount: 99.99,
      currency: 'USD',
    },
  },
  {
    id: '4',
    timestamp: '2024-01-15T10:33:45Z',
    level: 'debug',
    service: 'user-service',
    source: 'DatabaseConnection',
    message: 'Database query executed',
    metadata: {
      query: 'SELECT * FROM users WHERE id = ?',
      executionTime: '12ms',
    },
  },
  {
    id: '5',
    timestamp: '2024-01-15T10:33:20Z',
    level: 'error',
    service: 'order-service',
    source: 'InventoryService',
    message: 'Insufficient inventory for product',
    metadata: {
      productId: 'PROD-123',
      requested: 5,
      available: 2,
    },
  },
]

// 计算属性
const activeAlertsCount = computed(() => {
  return alerts.filter(alert => alert.status === 'active').length
})

const filteredLogs = computed(() => {
  return logs.filter(log => {
    const matchesSearch = logSearch.value === '' ||
      log.message.toLowerCase().includes(logSearch.value.toLowerCase()) ||
      log.service.toLowerCase().includes(logSearch.value.toLowerCase())

    const matchesLevel = logLevel.value === 'all' || log.level === logLevel.value
    const matchesService = logService.value === 'all' || log.service === logService.value

    return matchesSearch && matchesLevel && matchesService
  })
})

// 工具函数
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const getSeverityColor = (severity: string) => {
  const colorMap = {
    low: 'text-blue-700 bg-blue-100 border-blue-200',
    medium: 'text-yellow-700 bg-yellow-100 border-yellow-200',
    high: 'text-orange-700 bg-orange-100 border-orange-200',
    critical: 'text-red-700 bg-red-100 border-red-200',
  }
  return colorMap[severity as keyof typeof colorMap] || 'text-gray-700 bg-gray-100 border-gray-200'
}

const getSeverityText = (severity: string) => {
  const textMap = {
    low: '低',
    medium: '中',
    high: '高',
    critical: '严重',
  }
  return textMap[severity as keyof typeof textMap] || severity
}

const getStatusColor = (status: string) => {
  return status === 'active'
    ? 'text-red-700 bg-red-100'
    : 'text-green-700 bg-green-100'
}

const getLevelColor = (level: string) => {
  const colorMap = {
    error: 'text-red-700 bg-red-100',
    warn: 'text-yellow-700 bg-yellow-100',
    info: 'text-blue-700 bg-blue-100',
    debug: 'text-gray-700 bg-gray-100',
  }
  return colorMap[level as keyof typeof colorMap] || 'text-gray-700 bg-gray-100'
}

// 刷新所有图表
const refreshAllCharts = () => {
  cpuChartRef.value?.refresh()
  memoryChartRef.value?.refresh()
  networkChartRef.value?.refresh()
  requestChartRef.value?.refresh()
}

// 监听时间范围变化
watch(selectedTimeRange, () => {
  // 图表组件会自动响应timeRange prop的变化
  // 这里可以添加额外的逻辑，比如更新系统概览数据
})
</script>
