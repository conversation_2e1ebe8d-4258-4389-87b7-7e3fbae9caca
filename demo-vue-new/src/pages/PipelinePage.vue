<template>
  <div class="space-y-6">
    <!-- 页面标题和操作 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">CI/CD 流水线</h1>
        <p class="text-gray-600">持续集成与持续部署管理</p>
      </div>
      <button 
        @click="router.push('/pipeline/create')"
        class="btn-primary flex items-center space-x-2"
      >
        <Play class="h-4 w-4" />
        <span>新建流水线</span>
      </button>
    </div>

    <!-- 搜索和过滤 -->
    <div class="card p-4">
      <div class="flex flex-col sm:flex-row gap-4">
        <!-- 搜索框 -->
        <div class="flex-1">
          <div class="relative">
            <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="搜索流水线..."
              v-model="searchTerm"
              class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>
        </div>

        <!-- 状态过滤 -->
        <div class="flex items-center space-x-2">
          <Filter class="h-4 w-4 text-gray-400" />
          <select
            v-model="filterStatus"
            class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          >
            <option value="all">全部状态</option>
            <option value="running">运行中</option>
            <option value="success">成功</option>
            <option value="failed">失败</option>
            <option value="pending">等待中</option>
          </select>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 流水线列表 -->
      <div class="lg:col-span-1 space-y-4">
        <div
          v-for="pipeline in filteredPipelines"
          :key="pipeline.id"
          class="card p-4 cursor-pointer transition-all duration-200 hover:shadow-md"
          :class="[
            selectedPipeline?.id === pipeline.id
              ? 'ring-2 ring-primary-500 border-primary-200'
              : ''
          ]"
          @click="selectedPipeline = pipeline"
        >
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <div class="flex items-center space-x-2 mb-2">
                <component :is="statusIcons[pipeline.status]" :class="['h-4 w-4', statusColors[pipeline.status].split(' ')[0]]" />
                <h3 class="font-medium text-gray-900 truncate">
                  {{ pipeline.name }}
                </h3>
              </div>
              <p class="text-sm text-gray-600 mb-3 line-clamp-2">
                {{ pipeline.description }}
              </p>
              <div class="space-y-1 text-xs text-gray-500">
                <div class="flex items-center space-x-1">
                  <GitBranch class="h-3 w-3" />
                  <span>{{ pipeline.branch }}</span>
                </div>
                <div class="flex items-center space-x-1">
                  <User class="h-3 w-3" />
                  <span>{{ pipeline.author }}</span>
                </div>
                <div class="flex items-center space-x-1">
                  <Calendar class="h-3 w-3" />
                  <span>{{ new Date(pipeline.createdAt).toLocaleDateString('zh-CN') }}</span>
                </div>
              </div>
            </div>
            <div class="flex flex-col items-end space-y-2">
              <span :class="['status-badge', statusColors[pipeline.status]]">
                {{ getStatusText(pipeline.status) }}
              </span>
              <div class="flex space-x-1">
                <button
                  v-if="pipeline.status === 'running'"
                  @click.stop="handleStopPipeline(pipeline)"
                  class="p-1 text-red-600 hover:bg-red-50 rounded"
                  title="停止"
                >
                  <Square class="h-3 w-3" />
                </button>
                <button
                  v-else
                  @click.stop="handleRunPipeline(pipeline)"
                  class="p-1 text-green-600 hover:bg-green-50 rounded"
                  title="运行"
                >
                  <Play class="h-3 w-3" />
                </button>
                <button
                  @click.stop="handleRunPipeline(pipeline)"
                  class="p-1 text-blue-600 hover:bg-blue-50 rounded"
                  title="重新运行"
                >
                  <RotateCcw class="h-3 w-3" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 流水线可视化 -->
      <div class="lg:col-span-2">
        <PipelineVisualization v-if="selectedPipeline" :pipeline="selectedPipeline" />
        <div v-else class="card p-8 text-center">
          <GitBranch class="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 class="text-lg font-medium text-gray-900 mb-2">
            选择一个流水线
          </h3>
          <p class="text-gray-600">
            从左侧列表中选择一个流水线来查看详细的执行过程
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import {
  GitBranch,
  Play,
  Square,
  RotateCcw,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  User,
  Calendar,
  Filter,
  Search,
} from 'lucide-vue-next'
import PipelineVisualization from '@/components/pipeline/PipelineVisualization.vue'
import type { Pipeline, Status } from '@/types'

const router = useRouter()
const selectedPipeline = ref<Pipeline | null>(null)
const filterStatus = ref<Status | 'all'>('all')
const searchTerm = ref('')

// 模拟流水线数据
const mockPipelines: Pipeline[] = [
  {
    id: '1',
    name: 'user-service-pipeline',
    description: '用户服务持续集成流水线',
    status: 'success',
    stages: [
      {
        id: 'checkout',
        name: '代码检出',
        status: 'success',
        startTime: '2024-01-15T10:00:00Z',
        endTime: '2024-01-15T10:00:30Z',
        duration: 30,
        steps: [
          {
            id: 'git-checkout',
            name: 'Git Checkout',
            status: 'success',
            command: 'git checkout main',
            duration: 30,
          },
        ],
      },
      {
        id: 'build',
        name: '构建',
        status: 'success',
        startTime: '2024-01-15T10:00:30Z',
        endTime: '2024-01-15T10:02:00Z',
        duration: 90,
        steps: [
          {
            id: 'npm-install',
            name: 'Install Dependencies',
            status: 'success',
            command: 'npm install',
            duration: 60,
          },
          {
            id: 'npm-build',
            name: 'Build Application',
            status: 'success',
            command: 'npm run build',
            duration: 30,
          },
        ],
      },
    ],
    repository: {
      id: 'repo-1',
      name: 'user-service',
      url: 'https://github.com/company/user-service',
      provider: 'github',
    },
    branch: 'main',
    commitHash: 'abc123ef',
    commitMessage: 'feat: add user profile update API',
    author: '张三',
    duration: 360,
    triggeredBy: '代码推送',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:06:00Z',
  },
  {
    id: '2',
    name: 'order-service-pipeline',
    description: '订单服务持续集成流水线',
    status: 'running',
    stages: [
      {
        id: 'checkout',
        name: '代码检出',
        status: 'success',
        startTime: '2024-01-15T10:10:00Z',
        endTime: '2024-01-15T10:10:25Z',
        duration: 25,
        steps: [
          {
            id: 'git-checkout',
            name: 'Git Checkout',
            status: 'success',
            command: 'git checkout develop',
            duration: 25,
          },
        ],
      },
      {
        id: 'build',
        name: '构建',
        status: 'running',
        startTime: '2024-01-15T10:10:25Z',
        steps: [
          {
            id: 'npm-install',
            name: 'Install Dependencies',
            status: 'success',
            command: 'npm install',
            duration: 45,
          },
          {
            id: 'npm-build',
            name: 'Build Application',
            status: 'running',
            command: 'npm run build',
          },
        ],
      },
    ],
    repository: {
      id: 'repo-2',
      name: 'order-service',
      url: 'https://github.com/company/order-service',
      provider: 'github',
    },
    branch: 'develop',
    commitHash: 'def456gh',
    commitMessage: 'fix: order calculation bug',
    author: '李四',
    duration: 0,
    triggeredBy: '手动触发',
    createdAt: '2024-01-15T10:10:00Z',
    updatedAt: '2024-01-15T10:10:00Z',
  },
]

const statusIcons = {
  pending: AlertCircle,
  running: Clock,
  success: CheckCircle,
  failed: XCircle,
  cancelled: Square,
}

const statusColors = {
  pending: 'text-gray-500 bg-gray-100',
  running: 'text-blue-700 bg-blue-100',
  success: 'text-green-700 bg-green-100',
  failed: 'text-red-700 bg-red-100',
  cancelled: 'text-gray-700 bg-gray-100',
}

const filteredPipelines = computed(() => {
  return mockPipelines.filter((pipeline) => {
    const matchesStatus = filterStatus.value === 'all' || pipeline.status === filterStatus.value
    const matchesSearch = pipeline.name.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                         pipeline.description.toLowerCase().includes(searchTerm.value.toLowerCase())
    return matchesStatus && matchesSearch
  })
})

const getStatusText = (status: Status) => {
  const statusMap = {
    running: '运行中',
    success: '成功',
    failed: '失败',
    pending: '等待中',
    cancelled: '已取消'
  }
  return statusMap[status]
}

const handleRunPipeline = (pipeline: Pipeline) => {
  console.log('运行流水线:', pipeline.name)
  // 实际应用中会调用API
}

const handleStopPipeline = (pipeline: Pipeline) => {
  console.log('停止流水线:', pipeline.name)
  // 实际应用中会调用API
}
</script>
