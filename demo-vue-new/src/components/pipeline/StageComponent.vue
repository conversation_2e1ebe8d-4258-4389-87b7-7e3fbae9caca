<template>
  <div class="relative">
    <!-- 连接线 -->
    <div v-if="!isLast" class="absolute left-6 top-12 w-0.5 h-16 bg-gray-300"></div>
    
    <div class="flex items-start space-x-4">
      <!-- 状态图标 -->
      <div :class="['flex-shrink-0 w-12 h-12 rounded-full border-2 flex items-center justify-center', statusColors[stage.status]]">
        <div v-if="stage.status === 'running'" class="w-4 h-4 rounded-full border-2 border-blue-700 border-t-transparent animate-spin"></div>
        <component v-else :is="statusIcons[stage.status]" class="h-5 w-5" />
      </div>

      <!-- 阶段信息 -->
      <div class="flex-1 min-w-0">
        <div
          class="flex items-center justify-between cursor-pointer"
          @click="isExpanded = !isExpanded"
        >
          <div class="flex items-center space-x-2">
            <h3 class="text-lg font-medium text-gray-900">{{ stage.name }}</h3>
            <button v-if="stage.steps.length > 0" class="text-gray-400 hover:text-gray-600">
              <ChevronDown v-if="isExpanded" class="h-4 w-4" />
              <ChevronRight v-else class="h-4 w-4" />
            </button>
          </div>
          <div class="flex items-center space-x-4 text-sm text-gray-500">
            <div v-if="stage.duration" class="flex items-center space-x-1">
              <Timer class="h-4 w-4" />
              <span>{{ formatDuration(stage.duration) }}</span>
            </div>
            <span v-if="stage.startTime">{{ new Date(stage.startTime).toLocaleTimeString('zh-CN') }}</span>
          </div>
        </div>

        <!-- 步骤列表 -->
        <div v-if="isExpanded && stage.steps.length > 0" class="mt-4 ml-4 space-y-3">
          <StepComponent v-for="step in stage.steps" :key="step.id" :step="step" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  ChevronDown,
  ChevronRight,
  Timer,
} from 'lucide-vue-next'
import StepComponent from './StepComponent.vue'
import type { PipelineStage } from '@/types'

interface Props {
  stage: PipelineStage
  isLast: boolean
}

const props = defineProps<Props>()

const isExpanded = ref(props.stage.status === 'running' || props.stage.status === 'failed')

const statusIcons = {
  pending: AlertCircle,
  running: Clock,
  success: CheckCircle,
  failed: XCircle,
  cancelled: XCircle,
}

const statusColors = {
  pending: 'text-gray-500 bg-gray-100 border-gray-200',
  running: 'text-blue-700 bg-blue-100 border-blue-200',
  success: 'text-green-700 bg-green-100 border-green-200',
  failed: 'text-red-700 bg-red-100 border-red-200',
  cancelled: 'text-gray-700 bg-gray-100 border-gray-200',
}

const formatDuration = (seconds: number): string => {
  if (seconds < 60) return `${seconds}s`
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}m ${remainingSeconds}s`
}
</script>
