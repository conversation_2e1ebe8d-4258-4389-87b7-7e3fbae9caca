<template>
  <div class="border border-gray-200 rounded-lg p-4 bg-white">
    <div
      class="flex items-center justify-between cursor-pointer"
      @click="showLogs = !showLogs"
    >
      <div class="flex items-center space-x-3">
        <component :is="statusIcons[step.status]" :class="['h-4 w-4', statusColors[step.status].split(' ')[0]]" />
        <span class="font-medium text-gray-900">{{ step.name }}</span>
        <code v-if="step.command" class="text-xs bg-gray-100 px-2 py-1 rounded text-gray-700">
          {{ step.command }}
        </code>
      </div>
      <div class="flex items-center space-x-2">
        <span v-if="step.duration" class="text-sm text-gray-500">
          {{ formatDuration(step.duration) }}
        </span>
        <Terminal class="h-4 w-4 text-gray-400" />
      </div>
    </div>

    <!-- 日志输出 -->
    <div v-if="showLogs && (step.output || step.error)" class="mt-3 p-3 bg-gray-900 rounded text-green-400 text-sm font-mono overflow-x-auto">
      <div v-if="step.output" class="whitespace-pre-wrap">{{ step.output }}</div>
      <div v-if="step.error" class="text-red-400 whitespace-pre-wrap">{{ step.error }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Terminal,
} from 'lucide-vue-next'
import type { PipelineStep } from '@/types'

interface Props {
  step: PipelineStep
}

defineProps<Props>()

const showLogs = ref(false)

const statusIcons = {
  pending: AlertCircle,
  running: Clock,
  success: CheckCircle,
  failed: XCircle,
  cancelled: XCircle,
}

const statusColors = {
  pending: 'text-gray-500 bg-gray-100 border-gray-200',
  running: 'text-blue-700 bg-blue-100 border-blue-200',
  success: 'text-green-700 bg-green-100 border-green-200',
  failed: 'text-red-700 bg-red-100 border-red-200',
  cancelled: 'text-gray-700 bg-gray-100 border-gray-200',
}

const formatDuration = (seconds: number): string => {
  if (seconds < 60) return `${seconds}s`
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}m ${remainingSeconds}s`
}
</script>
