<template>
  <div class="card">
    <!-- 流水线头部信息 -->
    <div class="p-6 border-b border-gray-200">
      <div class="flex items-start justify-between">
        <div>
          <h2 class="text-xl font-bold text-gray-900 mb-2">
            {{ pipeline.name }}
          </h2>
          <p class="text-gray-600 mb-4">{{ pipeline.description }}</p>
          
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div class="flex items-center space-x-2">
              <GitCommit class="h-4 w-4 text-gray-400" />
              <div>
                <div class="text-gray-500">提交</div>
                <div class="font-medium">{{ pipeline.commitHash }}</div>
              </div>
            </div>
            <div class="flex items-center space-x-2">
              <User class="h-4 w-4 text-gray-400" />
              <div>
                <div class="text-gray-500">作者</div>
                <div class="font-medium">{{ pipeline.author }}</div>
              </div>
            </div>
            <div class="flex items-center space-x-2">
              <Calendar class="h-4 w-4 text-gray-400" />
              <div>
                <div class="text-gray-500">开始时间</div>
                <div class="font-medium">
                  {{ new Date(pipeline.createdAt).toLocaleString('zh-CN') }}
                </div>
              </div>
            </div>
            <div class="flex items-center space-x-2">
              <Timer class="h-4 w-4 text-gray-400" />
              <div>
                <div class="text-gray-500">总耗时</div>
                <div class="font-medium">
                  {{ pipeline.duration > 0 ? formatDuration(pipeline.duration) : '--' }}
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="flex items-center space-x-2">
          <span :class="['status-badge', statusColors[pipeline.status]]">
            {{ getStatusText(pipeline.status) }}
          </span>
        </div>
      </div>

      <!-- 提交信息 -->
      <div class="mt-4 p-3 bg-gray-50 rounded-lg">
        <div class="text-sm text-gray-600">最新提交:</div>
        <div class="font-medium text-gray-900">{{ pipeline.commitMessage }}</div>
      </div>
    </div>

    <!-- 流水线阶段 -->
    <div class="p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-6">执行阶段</h3>
      <div class="space-y-8">
        <StageComponent
          v-for="(stage, index) in pipeline.stages"
          :key="stage.id"
          :stage="stage"
          :is-last="index === pipeline.stages.length - 1"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  GitCommit,
  User,
  Calendar,
  Timer,
} from 'lucide-vue-next'
import StageComponent from './StageComponent.vue'
import type { Pipeline, Status } from '@/types'

interface Props {
  pipeline: Pipeline
}

defineProps<Props>()

const statusColors = {
  pending: 'text-gray-500 bg-gray-100 border-gray-200',
  running: 'text-blue-700 bg-blue-100 border-blue-200',
  success: 'text-green-700 bg-green-100 border-green-200',
  failed: 'text-red-700 bg-red-100 border-red-200',
  cancelled: 'text-gray-700 bg-gray-100 border-gray-200',
}

const formatDuration = (seconds: number): string => {
  if (seconds < 60) return `${seconds}s`
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}m ${remainingSeconds}s`
}

const getStatusText = (status: Status) => {
  const statusMap = {
    running: '运行中',
    success: '成功',
    failed: '失败',
    pending: '等待中',
    cancelled: '已取消'
  }
  return statusMap[status]
}
</script>
