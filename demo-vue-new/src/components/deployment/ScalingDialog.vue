<template>
  <div v-if="isOpen" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
      <!-- 背景遮罩 -->
      <div
        class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
        @click="$emit('close')"
      ></div>

      <!-- 对话框内容 -->
      <div class="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <!-- 标题 -->
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center space-x-2">
              <Activity class="h-5 w-5 text-primary-600" />
              <h3 class="text-lg font-medium text-gray-900">
                应用扩缩容
              </h3>
            </div>
            <button
              @click="$emit('close')"
              class="text-gray-400 hover:text-gray-600"
            >
              <X class="h-5 w-5" />
            </button>
          </div>

          <!-- 应用信息 -->
          <div v-if="application" class="mb-6 p-4 bg-gray-50 rounded-lg">
            <h4 class="font-medium text-gray-900 mb-2">{{ application.name }}</h4>
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span class="text-gray-500">当前实例:</span>
                <span class="ml-2 font-medium">{{ currentInstances }}</span>
              </div>
              <div>
                <span class="text-gray-500">运行实例:</span>
                <span class="ml-2 font-medium">{{ runningInstances }}</span>
              </div>
              <div>
                <span class="text-gray-500">CPU使用:</span>
                <span class="ml-2 font-medium">
                  {{ application.resources.cpu.current }}/{{ application.resources.cpu.limit }} {{ application.resources.cpu.unit }}
                </span>
              </div>
              <div>
                <span class="text-gray-500">内存使用:</span>
                <span class="ml-2 font-medium">
                  {{ application.resources.memory.current }}/{{ application.resources.memory.limit }} MB
                </span>
              </div>
            </div>
          </div>

          <form @submit="handleSubmit">
            <!-- 实例数量调整 -->
            <div class="mb-6">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                目标实例数量
              </label>
              <div class="flex items-center space-x-3">
                <button
                  type="button"
                  @click="targetInstances = Math.max(0, targetInstances - 1)"
                  :disabled="targetInstances <= 0"
                  class="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Minus class="h-4 w-4" />
                </button>

                <div class="flex-1 text-center">
                  <input
                    type="number"
                    min="0"
                    max="20"
                    v-model.number="targetInstances"
                    class="w-20 text-center text-lg font-medium border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                </div>

                <button
                  type="button"
                  @click="targetInstances = Math.min(20, targetInstances + 1)"
                  :disabled="targetInstances >= 20"
                  class="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Plus class="h-4 w-4" />
                </button>
              </div>

              <!-- 变化提示 -->
              <div v-if="targetInstances !== currentInstances" :class="[
                'mt-2 p-2 rounded text-sm flex items-center space-x-1',
                scalingType === 'scale-up'
                  ? 'bg-green-50 text-green-700'
                  : 'bg-yellow-50 text-yellow-700'
              ]">
                <TrendingUp v-if="scalingType === 'scale-up'" class="h-4 w-4" />
                <TrendingDown v-else class="h-4 w-4" />
                <span>
                  {{ scalingType === 'scale-up' ? '扩容' : '缩容' }} {{ instanceDiff }} 个实例
                  <span v-if="scalingType === 'scale-up'"> (新实例启动大约需要30-60秒)</span>
                </span>
              </div>
            </div>

            <!-- 扩缩容原因 -->
            <div class="mb-6">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                扩缩容原因 <span class="text-red-500">*</span>
              </label>
              <textarea
                v-model="reason"
                placeholder="请输入扩缩容原因..."
                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                rows="3"
                required
              />

              <!-- 建议原因 -->
              <div class="mt-2">
                <div class="text-xs text-gray-500 mb-1">常用原因:</div>
                <div class="flex flex-wrap gap-1">
                  <button
                    v-for="(suggestedReason, index) in filteredSuggestedReasons"
                    :key="index"
                    type="button"
                    @click="reason = suggestedReason"
                    class="text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
                  >
                    {{ suggestedReason }}
                  </button>
                </div>
              </div>
            </div>

            <!-- 警告信息 -->
            <div v-if="scalingType === 'scale-down' && targetInstances < runningInstances" class="mb-6 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div class="flex items-start space-x-2">
                <AlertTriangle class="h-4 w-4 text-yellow-600 mt-0.5" />
                <div class="text-sm text-yellow-700">
                  <div class="font-medium">缩容警告</div>
                  <div>将会停止 {{ Math.min(instanceDiff, runningInstances) }} 个正在运行的实例，可能会影响服务可用性。请确保当前负载允许缩容操作。</div>
                </div>
              </div>
            </div>

          </form>
        </div>

        <!-- 操作按钮 -->
        <div class="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
          <button
            type="submit"
            @click="handleSubmit"
            :disabled="isSubmitting || targetInstances === currentInstances || !reason.trim()"
            :class="[
              'w-full inline-flex justify-center rounded-md border border-transparent px-4 py-2 text-base font-medium text-white shadow-sm sm:ml-3 sm:w-auto sm:text-sm focus:outline-none focus:ring-2 focus:ring-offset-2',
              isSubmitting || targetInstances === currentInstances || !reason.trim()
                ? 'bg-gray-400 cursor-not-allowed'
                : scalingType === 'scale-up'
                ? 'bg-green-600 hover:bg-green-700 focus:ring-green-500'
                : 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500'
            ]"
          >
            {{ isSubmitting ? '执行中...' : `确认${scalingType === 'scale-up' ? '扩容' : '缩容'}` }}
          </button>
          <button
            type="button"
            @click="$emit('close')"
            :disabled="isSubmitting"
            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
          >
            取消
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import {
  X,
  Plus,
  Minus,
  Activity,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
} from 'lucide-vue-next'
import type { Application } from '@/types'

interface Props {
  isOpen: boolean
  application: Application | null
}

const props = defineProps<Props>()

const emit = defineEmits<{
  close: []
  confirm: [targetInstances: number, reason: string]
}>()

const targetInstances = ref(1)
const reason = ref('')
const isSubmitting = ref(false)

// 监听应用变化，重置表单
watch(() => props.application, (newApp) => {
  if (newApp && props.isOpen) {
    targetInstances.value = newApp.instances.length
    reason.value = ''
    isSubmitting.value = false
  }
})

// 计算属性
const currentInstances = computed(() => {
  return props.application?.instances.length || 0
})

const runningInstances = computed(() => {
  return props.application?.instances.filter(instance => instance.status === 'running').length || 0
})

const scalingType = computed(() => {
  return targetInstances.value > currentInstances.value ? 'scale-up' : 'scale-down'
})

const instanceDiff = computed(() => {
  return Math.abs(targetInstances.value - currentInstances.value)
})

// 建议原因
const suggestedReasons = [
  '负载增加，需要更多实例',
  '流量高峰期临时扩容',
  '性能测试扩容',
  '节约资源成本',
  '低峰期缩容',
  '维护期间缩容',
]

const filteredSuggestedReasons = computed(() => {
  return suggestedReasons.slice(0, scalingType.value === 'scale-up' ? 3 : 6)
})

// 表单提交
const handleSubmit = async (event: Event) => {
  event.preventDefault()

  if (targetInstances.value === currentInstances.value) {
    return
  }

  if (!reason.value.trim()) {
    return
  }

  isSubmitting.value = true

  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    emit('confirm', targetInstances.value, reason.value.trim())
    emit('close')
  } finally {
    isSubmitting.value = false
  }
}
</script>
