<template>
  <div class="card">
    <!-- 应用基本信息 -->
    <div class="p-6 border-b border-gray-200">
      <div class="flex items-start justify-between">
        <div class="flex-1">
          <div class="flex items-center space-x-3 mb-2">
            <Container class="h-6 w-6 text-primary-600" />
            <h3 class="text-xl font-semibold text-gray-900">
              {{ application.name }}
            </h3>
            <span :class="['inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border', statusInfo.color]">
              <component :is="statusInfo.icon" class="h-3 w-3 mr-1" />
              {{ statusInfo.label }}
            </span>
          </div>
          <p class="text-gray-600 mb-4">{{ application.description }}</p>

          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span class="text-gray-500">版本:</span>
              <span class="ml-2 font-medium">{{ application.version }}</span>
            </div>
            <div>
              <span class="text-gray-500">实例:</span>
              <span class="ml-2 font-medium">
                {{ runningInstances }}/{{ totalInstances }}
              </span>
            </div>
            <div>
              <span class="text-gray-500">部署策略:</span>
              <span class="ml-2 font-medium">
                {{ getDeploymentStrategyText(application.deploymentStrategy.type) }}
              </span>
            </div>
            <div>
              <span class="text-gray-500">健康检查:</span>
              <span :class="[
                'ml-2 font-medium',
                application.healthCheck.status === 'healthy' ? 'text-green-600' : 'text-red-600'
              ]">
                {{ application.healthCheck.status === 'healthy' ? '正常' : '异常' }}
              </span>
            </div>
          </div>
        </div>

        <div class="flex items-center space-x-2">
          <button
            @click="isExpanded = !isExpanded"
            class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded"
            :title="isExpanded ? '收起详情' : '展开详情'"
          >
            <ChevronDown v-if="isExpanded" class="h-4 w-4" />
            <ChevronRight v-else class="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>

    <!-- 资源使用情况 -->
    <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div class="flex items-center space-x-2">
          <Cpu class="h-4 w-4 text-blue-500" />
          <div>
            <div class="text-xs text-gray-500">CPU</div>
            <div :class="[
              'text-sm font-medium',
              getResourceUsageColor(application.resources.cpu.current, application.resources.cpu.limit)
            ]">
              {{ application.resources.cpu.current }}/{{ application.resources.cpu.limit }} {{ application.resources.cpu.unit }}
            </div>
          </div>
        </div>

        <div class="flex items-center space-x-2">
          <HardDrive class="h-4 w-4 text-green-500" />
          <div>
            <div class="text-xs text-gray-500">内存</div>
            <div :class="[
              'text-sm font-medium',
              getResourceUsageColor(application.resources.memory.current, application.resources.memory.limit)
            ]">
              {{ formatBytes(application.resources.memory.current, application.resources.memory.unit) }}/
              {{ formatBytes(application.resources.memory.limit, application.resources.memory.unit) }}
            </div>
          </div>
        </div>

        <div class="flex items-center space-x-2">
          <Network class="h-4 w-4 text-purple-500" />
          <div>
            <div class="text-xs text-gray-500">网络 I/O</div>
            <div class="text-sm font-medium text-gray-900">
              ↓{{ application.resources.network.inbound }} / ↑{{ application.resources.network.outbound }} {{ application.resources.network.unit }}
            </div>
          </div>
        </div>

        <div class="flex items-center space-x-2">
          <HardDrive class="h-4 w-4 text-orange-500" />
          <div>
            <div class="text-xs text-gray-500">存储</div>
            <div :class="[
              'text-sm font-medium',
              getResourceUsageColor(application.resources.storage.used, application.resources.storage.total)
            ]">
              {{ formatBytes(application.resources.storage.used, application.resources.storage.unit) }}/
              {{ formatBytes(application.resources.storage.total, application.resources.storage.unit) }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="px-6 py-4 border-b border-gray-200">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-2">
          <button
            @click="$emit('scale', props.application)"
            class="btn-primary flex items-center space-x-1 text-sm"
          >
            <Activity class="h-3 w-3" />
            <span>扩缩容</span>
          </button>

          <button
            @click="handleRestart"
            :disabled="isRestarting"
            class="btn-secondary flex items-center space-x-1 text-sm"
          >
            <RotateCcw :class="['h-3 w-3', { 'animate-spin': isRestarting }]" />
            <span>{{ isRestarting ? '重启中...' : '重启' }}</span>
          </button>

          <button class="btn-secondary flex items-center space-x-1 text-sm">
            <Settings class="h-3 w-3" />
            <span>配置</span>
          </button>
        </div>

        <div class="text-xs text-gray-500">
          最后更新: {{ new Date(application.updatedAt).toLocaleString('zh-CN') }}
        </div>
      </div>
    </div>

    <!-- 实例详情 -->
    <div v-if="isExpanded" class="px-6 py-4">
      <h4 class="text-sm font-medium text-gray-900 mb-3">实例列表</h4>
      <div class="space-y-3">
        <div
          v-for="instance in application.instances"
          :key="instance.id"
          class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
        >
          <div class="flex items-center space-x-3">
            <component :is="instanceStatusInfo[instance.status].icon" :class="['h-4 w-4', instanceStatusInfo[instance.status].color]" />
            <div>
              <div class="text-sm font-medium text-gray-900">
                {{ instance.name }}
              </div>
              <div class="text-xs text-gray-500">
                {{ instance.ip }}:{{ instance.port }} | {{ instance.nodeName }}
              </div>
            </div>
          </div>

          <div class="flex items-center space-x-6 text-xs text-gray-500">
            <div>
              <span>CPU: {{ instance.cpu }}</span>
            </div>
            <div>
              <span>内存: {{ instance.memory }}MB</span>
            </div>
            <div>
              <span>重启: {{ instance.restartCount }}次</span>
            </div>
            <div>
              <span>运行时间: {{ instance.uptime }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  Container,
  Activity,
  Settings,
  RotateCcw,
  ChevronDown,
  ChevronRight,
  Cpu,
  HardDrive,
  Network,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Loader,
} from 'lucide-vue-next'
import type { Application, ApplicationStatus } from '@/types'

interface Props {
  application: Application
}

const props = defineProps<Props>()

defineEmits<{
  scale: [application: Application]
}>()

const isExpanded = ref(false)
const isRestarting = ref(false)

// 状态信息映射
const statusInfo = computed(() => {
  const statusMap = {
    healthy: {
      label: '健康',
      icon: CheckCircle,
      color: 'text-green-700 bg-green-100 border-green-200',
    },
    unhealthy: {
      label: '异常',
      icon: XCircle,
      color: 'text-red-700 bg-red-100 border-red-200',
    },
    deploying: {
      label: '部署中',
      icon: Loader,
      color: 'text-blue-700 bg-blue-100 border-blue-200',
    },
    scaling: {
      label: '扩容中',
      icon: Activity,
      color: 'text-yellow-700 bg-yellow-100 border-yellow-200',
    },
    stopped: {
      label: '已停止',
      icon: AlertTriangle,
      color: 'text-gray-700 bg-gray-100 border-gray-200',
    },
  }
  return statusMap[props.application.status]
})

// 实例状态信息
const instanceStatusInfo = {
  running: {
    icon: CheckCircle,
    color: 'text-green-500',
  },
  pending: {
    icon: Clock,
    color: 'text-yellow-500',
  },
  failed: {
    icon: XCircle,
    color: 'text-red-500',
  },
  terminated: {
    icon: AlertTriangle,
    color: 'text-gray-500',
  },
}

// 计算运行实例数
const runningInstances = computed(() => {
  return props.application.instances.filter(instance => instance.status === 'running').length
})

const totalInstances = computed(() => {
  return props.application.instances.length
})

// 获取部署策略文本
const getDeploymentStrategyText = (strategy: string) => {
  const strategyMap = {
    rolling: '滚动更新',
    'blue-green': '蓝绿部署',
    canary: '金丝雀发布',
  }
  return strategyMap[strategy as keyof typeof strategyMap] || strategy
}

// 获取资源使用颜色
const getResourceUsageColor = (current: number, limit: number) => {
  const usage = (current / limit) * 100
  if (usage >= 90) return 'text-red-600'
  if (usage >= 70) return 'text-yellow-600'
  return 'text-green-600'
}

// 格式化字节数
const formatBytes = (value: number, unit: string) => {
  if (unit === 'MB' || unit === 'GB') {
    return `${value}${unit}`
  }
  return `${value} ${unit}`
}

// 重启应用
const handleRestart = async () => {
  isRestarting.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    console.log('重启应用:', props.application.name)
  } finally {
    isRestarting.value = false
  }
}
</script>
