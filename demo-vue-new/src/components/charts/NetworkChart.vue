<template>
  <div class="space-y-4">
    <div class="flex items-center justify-between">
      <h3 class="text-lg font-semibold text-gray-900">网络流量 (MB/s)</h3>
      <div class="flex items-center space-x-4 text-sm text-gray-600">
        <div class="flex items-center space-x-1">
          <div class="w-3 h-3 bg-purple-500 rounded-full"></div>
          <span>入站: {{ currentInbound.toFixed(1) }} MB/s</span>
        </div>
        <div class="flex items-center space-x-1">
          <div class="w-3 h-3 bg-pink-500 rounded-full"></div>
          <span>出站: {{ currentOutbound.toFixed(1) }} MB/s</span>
        </div>
      </div>
    </div>
    
    <LineChart
      :data="chartData"
      :options="chartOptions"
      :loading="loading"
      :height="200"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import LineChart from './LineChart.vue'
import { useMonitoringApi, type MetricDataPoint } from '@/services/monitoringApi'
import type { ChartData, ChartOptions } from 'chart.js'

interface Props {
  timeRange?: string
  autoRefresh?: boolean
  refreshInterval?: number
}

const props = withDefaults(defineProps<Props>(), {
  timeRange: '1h',
  autoRefresh: true,
  refreshInterval: 30000,
})

const monitoringApi = useMonitoringApi()
const loading = ref(false)
const inboundData = ref<MetricDataPoint[]>([])
const outboundData = ref<MetricDataPoint[]>([])
const refreshTimer = ref<NodeJS.Timeout>()

// 当前值
const currentInbound = computed(() => {
  if (inboundData.value.length === 0) return 0
  return inboundData.value[inboundData.value.length - 1]?.value || 0
})

const currentOutbound = computed(() => {
  if (outboundData.value.length === 0) return 0
  return outboundData.value[outboundData.value.length - 1]?.value || 0
})

// 图表数据
const chartData = computed<ChartData<'line'>>(() => {
  if (!inboundData.value || inboundData.value.length === 0 || !outboundData.value || outboundData.value.length === 0) {
    return {
      labels: [],
      datasets: [
        {
          label: '入站流量',
          data: [],
          borderColor: 'rgb(147, 51, 234)',
          backgroundColor: 'rgba(147, 51, 234, 0.1)',
          fill: false,
          tension: 0.4,
          pointRadius: 2,
          pointHoverRadius: 4,
          borderWidth: 2,
        },
        {
          label: '出站流量',
          data: [],
          borderColor: 'rgb(236, 72, 153)',
          backgroundColor: 'rgba(236, 72, 153, 0.1)',
          fill: false,
          tension: 0.4,
          pointRadius: 2,
          pointHoverRadius: 4,
          borderWidth: 2,
        },
      ],
    }
  }

  const labels = inboundData.value.map(point => {
    const date = new Date(point.timestamp)
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    })
  })

  return {
    labels,
    datasets: [
      {
        label: '入站流量',
        data: inboundData.value.map(point => point.value),
        borderColor: 'rgb(147, 51, 234)',
        backgroundColor: 'rgba(147, 51, 234, 0.1)',
        fill: false,
        tension: 0.4,
        pointRadius: 2,
        pointHoverRadius: 4,
        borderWidth: 2,
      },
      {
        label: '出站流量',
        data: outboundData.value.map(point => point.value),
        borderColor: 'rgb(236, 72, 153)',
        backgroundColor: 'rgba(236, 72, 153, 0.1)',
        fill: false,
        tension: 0.4,
        pointRadius: 2,
        pointHoverRadius: 4,
        borderWidth: 2,
      },
    ],
  }
})

// 图表配置
const chartOptions = computed<ChartOptions<'line'>>(() => ({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: true,
      position: 'top' as const,
      labels: {
        usePointStyle: true,
        padding: 20,
        font: {
          size: 12,
        },
      },
    },
    tooltip: {
      callbacks: {
        label: (context) => `${context.dataset.label}: ${context.parsed.y.toFixed(1)} MB/s`,
      },
    },
  },
  scales: {
    x: {
      display: true,
      grid: {
        color: 'rgba(0, 0, 0, 0.05)',
      },
      ticks: {
        color: '#6B7280',
        maxTicksLimit: 8,
      },
    },
    y: {
      display: true,
      min: 0,
      grid: {
        color: 'rgba(0, 0, 0, 0.05)',
      },
      ticks: {
        color: '#6B7280',
        callback: (value) => `${value} MB/s`,
      },
    },
  },
  elements: {
    point: {
      radius: 0,
      hoverRadius: 4,
    },
  },
}))

// 获取数据
const fetchData = async () => {
  try {
    loading.value = true
    const metrics = await monitoringApi.getSystemMetrics(props.timeRange)
    inboundData.value = metrics.network.inbound
    outboundData.value = metrics.network.outbound
  } catch (error) {
    console.error('Failed to fetch network data:', error)
  } finally {
    loading.value = false
  }
}

// 设置自动刷新
const setupAutoRefresh = () => {
  if (props.autoRefresh) {
    refreshTimer.value = setInterval(fetchData, props.refreshInterval)
  }
}

// 清理定时器
const clearAutoRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = undefined
  }
}

onMounted(() => {
  fetchData()
  setupAutoRefresh()
})

onUnmounted(() => {
  clearAutoRefresh()
})

// 暴露刷新方法
defineExpose({
  refresh: fetchData,
})
</script>
