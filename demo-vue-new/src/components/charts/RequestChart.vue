<template>
  <div class="space-y-4">
    <div class="flex items-center justify-between">
      <h3 class="text-lg font-semibold text-gray-900">请求统计</h3>
      <div class="flex items-center space-x-4 text-sm text-gray-600">
        <div class="flex items-center space-x-1">
          <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
          <span>总计: {{ currentTotal }}</span>
        </div>
        <div class="flex items-center space-x-1">
          <div class="w-3 h-3 bg-green-500 rounded-full"></div>
          <span>成功: {{ currentSuccess }}</span>
        </div>
        <div class="flex items-center space-x-1">
          <div class="w-3 h-3 bg-red-500 rounded-full"></div>
          <span>错误: {{ currentError }}</span>
        </div>
        <div class="text-xs">
          <span class="text-green-600">成功率: {{ successRate }}%</span>
        </div>
      </div>
    </div>
    
    <LineChart
      :data="chartData"
      :options="chartOptions"
      :loading="loading"
      :height="200"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import LineChart from './LineChart.vue'
import { useMonitoringApi, type MetricDataPoint } from '@/services/monitoringApi'
import type { ChartData, ChartOptions } from 'chart.js'

interface Props {
  timeRange?: string
  autoRefresh?: boolean
  refreshInterval?: number
}

const props = withDefaults(defineProps<Props>(), {
  timeRange: '1h',
  autoRefresh: true,
  refreshInterval: 30000,
})

const monitoringApi = useMonitoringApi()
const loading = ref(false)
const totalData = ref<MetricDataPoint[]>([])
const successData = ref<MetricDataPoint[]>([])
const errorData = ref<MetricDataPoint[]>([])
const refreshTimer = ref<NodeJS.Timeout>()

// 当前值
const currentTotal = computed(() => {
  if (totalData.value.length === 0) return 0
  return Math.round(totalData.value[totalData.value.length - 1]?.value || 0)
})

const currentSuccess = computed(() => {
  if (successData.value.length === 0) return 0
  return Math.round(successData.value[successData.value.length - 1]?.value || 0)
})

const currentError = computed(() => {
  if (errorData.value.length === 0) return 0
  return Math.round(errorData.value[errorData.value.length - 1]?.value || 0)
})

const successRate = computed(() => {
  if (currentTotal.value === 0) return 0
  return Math.round((currentSuccess.value / currentTotal.value) * 100)
})

// 图表数据
const chartData = computed<ChartData<'line'>>(() => {
  if (!totalData.value || totalData.value.length === 0 ||
      !successData.value || successData.value.length === 0 ||
      !errorData.value || errorData.value.length === 0) {
    return {
      labels: [],
      datasets: [
        {
          label: '总请求',
          data: [],
          borderColor: 'rgb(59, 130, 246)',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          fill: false,
          tension: 0.4,
          pointRadius: 2,
          pointHoverRadius: 4,
          borderWidth: 2,
        },
        {
          label: '成功请求',
          data: [],
          borderColor: 'rgb(34, 197, 94)',
          backgroundColor: 'rgba(34, 197, 94, 0.1)',
          fill: false,
          tension: 0.4,
          pointRadius: 2,
          pointHoverRadius: 4,
          borderWidth: 2,
        },
        {
          label: '错误请求',
          data: [],
          borderColor: 'rgb(239, 68, 68)',
          backgroundColor: 'rgba(239, 68, 68, 0.1)',
          fill: false,
          tension: 0.4,
          pointRadius: 2,
          pointHoverRadius: 4,
          borderWidth: 2,
        },
      ],
    }
  }

  const labels = totalData.value.map(point => {
    const date = new Date(point.timestamp)
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    })
  })

  return {
    labels,
    datasets: [
      {
        label: '总请求',
        data: totalData.value.map(point => point.value),
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        fill: false,
        tension: 0.4,
        pointRadius: 2,
        pointHoverRadius: 4,
        borderWidth: 2,
      },
      {
        label: '成功请求',
        data: successData.value.map(point => point.value),
        borderColor: 'rgb(34, 197, 94)',
        backgroundColor: 'rgba(34, 197, 94, 0.1)',
        fill: false,
        tension: 0.4,
        pointRadius: 2,
        pointHoverRadius: 4,
        borderWidth: 2,
      },
      {
        label: '错误请求',
        data: errorData.value.map(point => point.value),
        borderColor: 'rgb(239, 68, 68)',
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        fill: false,
        tension: 0.4,
        pointRadius: 2,
        pointHoverRadius: 4,
        borderWidth: 2,
      },
    ],
  }
})

// 图表配置
const chartOptions = computed<ChartOptions<'line'>>(() => ({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: true,
      position: 'top' as const,
      labels: {
        usePointStyle: true,
        padding: 20,
        font: {
          size: 12,
        },
      },
    },
    tooltip: {
      callbacks: {
        label: (context) => `${context.dataset.label}: ${Math.round(context.parsed.y)}`,
      },
    },
  },
  scales: {
    x: {
      display: true,
      grid: {
        color: 'rgba(0, 0, 0, 0.05)',
      },
      ticks: {
        color: '#6B7280',
        maxTicksLimit: 8,
      },
    },
    y: {
      display: true,
      min: 0,
      grid: {
        color: 'rgba(0, 0, 0, 0.05)',
      },
      ticks: {
        color: '#6B7280',
        callback: (value) => Math.round(Number(value)).toString(),
      },
    },
  },
  elements: {
    point: {
      radius: 0,
      hoverRadius: 4,
    },
  },
}))

// 获取数据
const fetchData = async () => {
  try {
    loading.value = true
    const metrics = await monitoringApi.getSystemMetrics(props.timeRange)
    totalData.value = metrics.requests.total
    successData.value = metrics.requests.success
    errorData.value = metrics.requests.error
  } catch (error) {
    console.error('Failed to fetch request data:', error)
  } finally {
    loading.value = false
  }
}

// 设置自动刷新
const setupAutoRefresh = () => {
  if (props.autoRefresh) {
    refreshTimer.value = setInterval(fetchData, props.refreshInterval)
  }
}

// 清理定时器
const clearAutoRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = undefined
  }
}

onMounted(() => {
  fetchData()
  setupAutoRefresh()
})

onUnmounted(() => {
  clearAutoRefresh()
})

// 暴露刷新方法
defineExpose({
  refresh: fetchData,
})
</script>
