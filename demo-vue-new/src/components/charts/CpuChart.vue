<template>
  <div class="space-y-4">
    <div class="flex items-center justify-between">
      <h3 class="text-lg font-semibold text-gray-900">CPU使用率 (%)</h3>
      <div class="flex items-center space-x-2 text-sm text-gray-600">
        <div class="flex items-center space-x-1">
          <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
          <span>当前: {{ currentValue }}%</span>
        </div>
        <div class="flex items-center space-x-1">
          <TrendingUp v-if="trend > 0" class="h-4 w-4 text-green-500" />
          <TrendingDown v-else-if="trend < 0" class="h-4 w-4 text-red-500" />
          <Minus v-else class="h-4 w-4 text-gray-500" />
          <span :class="[
            trend > 0 ? 'text-green-600' : trend < 0 ? 'text-red-600' : 'text-gray-600'
          ]">
            {{ trend > 0 ? '+' : '' }}{{ trend }}%
          </span>
        </div>
      </div>
    </div>
    
    <LineChart
      :data="chartData"
      :options="chartOptions"
      :loading="loading"
      :height="200"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { TrendingUp, TrendingDown, Minus } from 'lucide-vue-next'
import LineChart from './LineChart.vue'
import { useMonitoringApi, type MetricDataPoint } from '@/services/monitoringApi'
import type { ChartData, ChartOptions } from 'chart.js'

interface Props {
  timeRange?: string
  autoRefresh?: boolean
  refreshInterval?: number
}

const props = withDefaults(defineProps<Props>(), {
  timeRange: '1h',
  autoRefresh: true,
  refreshInterval: 30000, // 30秒
})

const monitoringApi = useMonitoringApi()
const loading = ref(false)
const cpuData = ref<MetricDataPoint[]>([])
const refreshTimer = ref<NodeJS.Timeout>()

// 当前值和趋势
const currentValue = computed(() => {
  if (cpuData.value.length === 0) return 0
  return cpuData.value[cpuData.value.length - 1]?.value || 0
})

const trend = computed(() => {
  if (cpuData.value.length < 2) return 0
  const current = cpuData.value[cpuData.value.length - 1]?.value || 0
  const previous = cpuData.value[cpuData.value.length - 2]?.value || 0
  return Math.round((current - previous) * 100) / 100
})

// 图表数据
const chartData = computed<ChartData<'line'>>(() => {
  if (!cpuData.value || cpuData.value.length === 0) {
    return {
      labels: [],
      datasets: [
        {
          label: 'CPU使用率',
          data: [],
          borderColor: 'rgb(59, 130, 246)',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          fill: true,
          tension: 0.4,
          pointRadius: 2,
          pointHoverRadius: 4,
          borderWidth: 2,
        },
      ],
    }
  }

  const labels = cpuData.value.map(point => {
    const date = new Date(point.timestamp)
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    })
  })

  return {
    labels,
    datasets: [
      {
        label: 'CPU使用率',
        data: cpuData.value.map(point => point.value),
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        fill: true,
        tension: 0.4,
        pointRadius: 2,
        pointHoverRadius: 4,
        borderWidth: 2,
      },
    ],
  }
})

// 图表配置
const chartOptions = computed<ChartOptions<'line'>>(() => ({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false,
    },
    tooltip: {
      callbacks: {
        label: (context) => `CPU: ${context.parsed.y}%`,
      },
    },
  },
  scales: {
    x: {
      display: true,
      grid: {
        color: 'rgba(0, 0, 0, 0.05)',
      },
      ticks: {
        color: '#6B7280',
        maxTicksLimit: 8,
      },
    },
    y: {
      display: true,
      min: 0,
      max: 100,
      grid: {
        color: 'rgba(0, 0, 0, 0.05)',
      },
      ticks: {
        color: '#6B7280',
        callback: (value) => `${value}%`,
      },
    },
  },
  elements: {
    point: {
      radius: 0,
      hoverRadius: 4,
    },
  },
}))

// 获取数据
const fetchData = async () => {
  try {
    loading.value = true
    const metrics = await monitoringApi.getSystemMetrics(props.timeRange)
    cpuData.value = metrics.cpu
  } catch (error) {
    console.error('Failed to fetch CPU data:', error)
  } finally {
    loading.value = false
  }
}

// 设置自动刷新
const setupAutoRefresh = () => {
  if (props.autoRefresh) {
    refreshTimer.value = setInterval(fetchData, props.refreshInterval)
  }
}

// 清理定时器
const clearAutoRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = undefined
  }
}

onMounted(() => {
  fetchData()
  setupAutoRefresh()
})

onUnmounted(() => {
  clearAutoRefresh()
})

// 暴露刷新方法
defineExpose({
  refresh: fetchData,
})
</script>
