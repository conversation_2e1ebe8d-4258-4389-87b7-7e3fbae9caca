<template>
  <div class="relative">
    <canvas ref="chartRef"></canvas>
    <div v-if="loading" class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75">
      <div class="flex items-center space-x-2">
        <div class="w-4 h-4 border-2 border-primary-600 border-t-transparent rounded-full animate-spin"></div>
        <span class="text-sm text-gray-600">加载中...</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  LineController,
  Title,
  Tooltip,
  Legend,
  Filler,
  type ChartData,
  type ChartOptions,
} from 'chart.js'

// 注册Chart.js组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  LineController,
  Title,
  Toolt<PERSON>,
  Legend,
  Filler
)

interface Props {
  data: ChartData<'line'>
  options?: ChartOptions<'line'>
  loading?: boolean
  height?: number
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  height: 300,
})

const chartRef = ref<HTMLCanvasElement>()
let chartInstance: ChartJS<'line'> | null = null

const defaultOptions: ChartOptions<'line'> = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const,
      labels: {
        usePointStyle: true,
        padding: 20,
      },
    },
    tooltip: {
      mode: 'index',
      intersect: false,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      titleColor: '#fff',
      bodyColor: '#fff',
      borderColor: 'rgba(255, 255, 255, 0.1)',
      borderWidth: 1,
    },
  },
  scales: {
    x: {
      display: true,
      grid: {
        color: 'rgba(0, 0, 0, 0.1)',
      },
      ticks: {
        color: '#6B7280',
      },
    },
    y: {
      display: true,
      grid: {
        color: 'rgba(0, 0, 0, 0.1)',
      },
      ticks: {
        color: '#6B7280',
      },
    },
  },
  elements: {
    line: {
      tension: 0.4,
    },
    point: {
      radius: 3,
      hoverRadius: 6,
    },
  },
  interaction: {
    mode: 'nearest',
    axis: 'x',
    intersect: false,
  },
}

const createChart = () => {
  if (!chartRef.value) {
    console.warn('Chart canvas ref not available')
    return
  }

  const ctx = chartRef.value.getContext('2d')
  if (!ctx) {
    console.warn('Failed to get 2d context from canvas')
    return
  }

  try {
    chartInstance = new ChartJS(ctx, {
      type: 'line',
      data: props.data,
      options: {
        ...defaultOptions,
        ...props.options,
      },
    })
  } catch (error) {
    console.error('Failed to create chart:', error)
  }
}

const updateChart = () => {
  if (chartInstance && props.data) {
    try {
      chartInstance.data = props.data
      chartInstance.update('none')
    } catch (error) {
      console.error('Failed to update chart:', error)
    }
  }
}

const destroyChart = () => {
  if (chartInstance) {
    chartInstance.destroy()
    chartInstance = null
  }
}

onMounted(async () => {
  await nextTick()
  // 等待数据加载完成后再创建图表
  if (props.data && props.data.labels && props.data.labels.length > 0) {
    createChart()
  }
})

onUnmounted(() => {
  destroyChart()
})

watch(() => props.data, (newData) => {
  if (newData && newData.labels && newData.labels.length > 0) {
    if (chartInstance) {
      updateChart()
    } else {
      // 如果图表还没创建，现在创建它
      nextTick(() => {
        createChart()
      })
    }
  }
}, { deep: true })

watch(() => props.options, () => {
  destroyChart()
  nextTick(() => {
    createChart()
  })
}, { deep: true })
</script>

<style scoped>
canvas {
  max-height: v-bind('props.height + "px"');
}
</style>
