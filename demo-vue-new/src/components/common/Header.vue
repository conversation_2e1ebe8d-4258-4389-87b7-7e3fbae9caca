<template>
  <header class="bg-white border-b border-gray-200 px-6 py-4">
    <div class="flex items-center justify-between">
      <!-- 搜索栏 -->
      <div class="flex-1 max-w-md">
        <div class="relative">
          <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="text"
            placeholder="搜索应用、流水线、日志..."
            class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
        </div>
      </div>

      <!-- 右侧操作区 -->
      <div class="flex items-center space-x-4">
        <!-- 刷新按钮 -->
        <button
          @click="handleRefresh"
          class="p-2 text-gray-400 hover:text-gray-600 transition-colors duration-200"
          :disabled="isRefreshing"
        >
          <RefreshCw :class="['h-5 w-5', { 'animate-spin': isRefreshing }]" />
        </button>

        <!-- 通知 -->
        <div class="relative">
          <button class="p-2 text-gray-400 hover:text-gray-600 transition-colors duration-200">
            <Bell class="h-5 w-5" />
          </button>
          <span class="absolute top-1 right-1 h-2 w-2 bg-red-500 rounded-full"></span>
        </div>

        <!-- 用户信息 -->
        <div class="flex items-center space-x-3">
          <div class="text-right">
            <div class="text-sm font-medium text-gray-900">管理员</div>
            <div class="text-xs text-gray-500"><EMAIL></div>
          </div>
          <div class="h-8 w-8 bg-primary-600 rounded-full flex items-center justify-center">
            <User class="h-4 w-4 text-white" />
          </div>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Bell, User, Search, RefreshCw } from 'lucide-vue-next'

const isRefreshing = ref(false)

const handleRefresh = () => {
  isRefreshing.value = true
  // 模拟刷新操作
  setTimeout(() => {
    isRefreshing.value = false
  }, 1000)
}
</script>
