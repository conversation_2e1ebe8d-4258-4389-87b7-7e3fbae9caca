<template>
  <div class="flex flex-col w-64 bg-white border-r border-gray-200">
    <!-- Logo -->
    <div class="flex items-center justify-center h-16 px-4 border-b border-gray-200">
      <div class="flex items-center space-x-2">
        <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
          <Container class="w-5 h-5 text-white" />
        </div>
        <div class="flex flex-col">
          <span class="text-lg font-bold text-gray-900">DevOps</span>
          <span class="text-xs text-gray-500">演示系统</span>
        </div>
      </div>
    </div>

    <!-- Navigation -->
    <nav class="flex-1 px-4 py-6 space-y-2">
      <RouterLink
        v-for="item in navigation"
        :key="item.name"
        :to="item.href"
        class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200"
        :class="[
          $route.path === item.href
            ? 'bg-primary-50 text-primary-700 border-r-2 border-primary-700'
            : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
        ]"
      >
        <component :is="item.icon" class="mr-3 h-5 w-5" />
        {{ item.name }}
      </RouterLink>
    </nav>

    <!-- Settings -->
    <div class="px-4 py-4 border-t border-gray-200">
      <RouterLink
        to="/settings"
        class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 transition-colors duration-200"
      >
        <Settings class="mr-3 h-5 w-5" />
        设置
      </RouterLink>
    </div>
  </div>
</template>

<script setup lang="ts">
import { RouterLink, useRoute } from 'vue-router'
import {
  LayoutDashboard,
  GitBranch,
  Container,
  Activity,
  Network,
  Settings,
} from 'lucide-vue-next'

const $route = useRoute()

const navigation = [
  {
    name: '总览',
    href: '/dashboard',
    icon: LayoutDashboard,
  },
  {
    name: 'CI/CD 流水线',
    href: '/pipeline',
    icon: GitBranch,
  },
  {
    name: '容器部署',
    href: '/deployment',
    icon: Container,
  },
  {
    name: '监控中心',
    href: '/monitoring',
    icon: Activity,
  },
  {
    name: '服务拓扑',
    href: '/topology',
    icon: Network,
  },
]
</script>
