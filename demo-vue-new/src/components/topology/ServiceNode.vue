<template>
  <div 
    class="absolute bg-white border-2 rounded-lg p-4 shadow-lg cursor-pointer transition-all duration-200 hover:shadow-xl"
    :class="[
      getStatusBorderClass(node.status),
      { 'ring-2 ring-primary-500': isSelected }
    ]"
    :style="{ 
      left: `${node.position.x}px`, 
      top: `${node.position.y}px`,
      width: '160px'
    }"
    @click="$emit('select', node)"
  >
    <div class="flex items-center space-x-2 mb-2">
      <component :is="getTypeIcon(node.type)" class="h-5 w-5 text-gray-600" />
      <h3 class="font-medium text-gray-900 text-sm truncate">{{ node.name }}</h3>
    </div>
    
    <div class="space-y-1 text-xs text-gray-500">
      <div class="flex justify-between">
        <span>类型:</span>
        <span>{{ getTypeText(node.type) }}</span>
      </div>
      <div class="flex justify-between">
        <span>实例:</span>
        <span>{{ node.instances }}</span>
      </div>
      <div class="flex justify-between">
        <span>状态:</span>
        <span :class="getStatusTextClass(node.status)">
          {{ getStatusText(node.status) }}
        </span>
      </div>
    </div>

    <!-- 连接点 -->
    <div class="absolute -right-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-gray-400 rounded-full"></div>
    <div class="absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-gray-400 rounded-full"></div>
  </div>
</template>

<script setup lang="ts">
import { Server, Database, Globe, Shield } from 'lucide-vue-next'
import type { ServiceNode, ApplicationStatus } from '@/types'

interface Props {
  node: ServiceNode
  isSelected?: boolean
}

defineProps<Props>()

defineEmits<{
  select: [node: ServiceNode]
}>()

const getTypeIcon = (type: string) => {
  const iconMap = {
    service: Server,
    database: Database,
    external: Globe,
    gateway: Shield,
  }
  return iconMap[type as keyof typeof iconMap] || Server
}

const getTypeText = (type: string) => {
  const textMap = {
    service: '服务',
    database: '数据库',
    external: '外部服务',
    gateway: '网关',
  }
  return textMap[type as keyof typeof textMap] || '服务'
}

const getStatusBorderClass = (status: ApplicationStatus) => {
  const classMap = {
    healthy: 'border-green-400',
    unhealthy: 'border-red-400',
    deploying: 'border-blue-400',
    scaling: 'border-yellow-400',
    stopped: 'border-gray-400',
  }
  return classMap[status]
}

const getStatusTextClass = (status: ApplicationStatus) => {
  const classMap = {
    healthy: 'text-green-600',
    unhealthy: 'text-red-600',
    deploying: 'text-blue-600',
    scaling: 'text-yellow-600',
    stopped: 'text-gray-600',
  }
  return classMap[status]
}

const getStatusText = (status: ApplicationStatus) => {
  const statusMap = {
    healthy: '健康',
    unhealthy: '异常',
    deploying: '部署中',
    scaling: '扩容中',
    stopped: '已停止',
  }
  return statusMap[status]
}
</script>
