import { createRouter, createWebHistory } from 'vue-router'
import Layout from '@/components/common/Layout.vue'
import Dashboard from '@/pages/Dashboard.vue'
import PipelinePage from '@/pages/PipelinePage.vue'
import CreatePipelinePage from '@/pages/CreatePipelinePage.vue'
import DeploymentPage from '@/pages/DeploymentPage.vue'
import CreateApplicationPage from '@/pages/CreateApplicationPage.vue'
import MonitoringPage from '@/pages/MonitoringPage.vue'
import TopologyPage from '@/pages/TopologyPage.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      component: Layout,
      children: [
        {
          path: '',
          redirect: '/dashboard'
        },
        {
          path: 'dashboard',
          name: 'Dashboard',
          component: Dashboard
        },
        {
          path: 'pipeline',
          name: 'Pipeline',
          component: PipelinePage
        },
        {
          path: 'pipeline/create',
          name: 'CreatePipeline',
          component: CreatePipelinePage
        },
        {
          path: 'deployment',
          name: 'Deployment',
          component: DeploymentPage
        },
        {
          path: 'deployment/create',
          name: 'CreateApplication',
          component: CreateApplicationPage
        },
        {
          path: 'monitoring',
          name: 'Monitoring',
          component: MonitoringPage
        },
        {
          path: 'topology',
          name: 'Topology',
          component: TopologyPage
        }
      ]
    }
  ]
})

export default router
