{"name": "devops-demo-vue", "version": "1.0.0", "description": "微服务持续交付开发及容器化管理部署过程演示系统 - Vue3版本", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "tsc --noEmit"}, "dependencies": {"@vueuse/core": "^10.7.0", "axios": "^1.6.2", "chart.js": "^4.5.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "echarts": "^5.4.3", "lucide-vue-next": "^0.294.0", "pinia": "^2.1.7", "vue": "^3.4.0", "vue-chartjs": "^5.3.2", "vue-echarts": "^6.6.1", "vue-router": "^4.2.5", "vue-toastification": "^2.0.0-rc.5"}, "devDependencies": {"@types/node": "^20.10.0", "@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.0", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-vue": "^9.18.1", "postcss": "^8.4.31", "prettier": "^3.1.0", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^5.0.0", "vue-tsc": "^1.8.25"}}