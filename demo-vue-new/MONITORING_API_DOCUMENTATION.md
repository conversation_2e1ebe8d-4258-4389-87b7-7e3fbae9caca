# 监控中心API接口文档

## 概述

本文档描述了监控中心所需的API接口，包括系统指标、告警管理和日志查询等功能。所有接口都已在代码中预留，使用MOCK数据进行开发，后续可以无缝接入真实的后端服务。

## 基础配置

### API基础地址
```
开发环境: https://api.devops-demo.com (MOCK数据)
生产环境: 通过环境变量 VITE_API_BASE_URL 配置
```

### 认证方式
```
Authorization: Bearer <token>
Content-Type: application/json
```

## 接口列表

### 1. 系统概览

#### 获取系统概览数据
```http
GET /api/monitoring/overview
```

**响应示例:**
```json
{
  "cpu": {
    "current": 58.2,
    "trend": 2.4
  },
  "memory": {
    "current": 1536,
    "total": 2048,
    "trend": -120
  },
  "network": {
    "inbound": 21.3,
    "outbound": 8.7,
    "trend": 3.2
  },
  "alerts": {
    "active": 3,
    "trend": -1
  }
}
```

**字段说明:**
- `cpu.current`: 当前CPU使用率 (%)
- `cpu.trend`: CPU使用率趋势变化 (%)
- `memory.current`: 当前内存使用量 (MB)
- `memory.total`: 总内存容量 (MB)
- `memory.trend`: 内存使用趋势变化 (MB)
- `network.inbound`: 入站网络流量 (MB/s)
- `network.outbound`: 出站网络流量 (MB/s)
- `network.trend`: 网络流量趋势变化 (MB/s)
- `alerts.active`: 活跃告警数量
- `alerts.trend`: 告警数量趋势变化

### 2. 系统指标数据

#### 获取时间序列指标数据
```http
GET /api/monitoring/metrics?timeRange={timeRange}
```

**请求参数:**
- `timeRange`: 时间范围 (5m, 15m, 1h, 6h, 24h)

**响应示例:**
```json
{
  "cpu": [
    {
      "timestamp": "2024-01-15T10:00:00Z",
      "value": 58.2
    }
  ],
  "memory": [
    {
      "timestamp": "2024-01-15T10:00:00Z",
      "value": 75.5
    }
  ],
  "network": {
    "inbound": [
      {
        "timestamp": "2024-01-15T10:00:00Z",
        "value": 21.3
      }
    ],
    "outbound": [
      {
        "timestamp": "2024-01-15T10:00:00Z",
        "value": 8.7
      }
    ]
  },
  "requests": {
    "total": [
      {
        "timestamp": "2024-01-15T10:00:00Z",
        "value": 350
      }
    ],
    "success": [
      {
        "timestamp": "2024-01-15T10:00:00Z",
        "value": 332
      }
    ],
    "error": [
      {
        "timestamp": "2024-01-15T10:00:00Z",
        "value": 18
      }
    ]
  }
}
```

**字段说明:**
- `cpu`: CPU使用率时间序列数据 (%)
- `memory`: 内存使用率时间序列数据 (%)
- `network.inbound`: 入站网络流量时间序列数据 (MB/s)
- `network.outbound`: 出站网络流量时间序列数据 (MB/s)
- `requests.total`: 总请求数时间序列数据
- `requests.success`: 成功请求数时间序列数据
- `requests.error`: 错误请求数时间序列数据

### 3. 告警管理

#### 获取告警列表
```http
GET /api/monitoring/alerts?status={status}&page={page}&limit={limit}
```

**请求参数:**
- `status`: 告警状态 (active, resolved) - 可选
- `page`: 页码，默认1
- `limit`: 每页数量，默认20

**响应示例:**
```json
{
  "data": [
    {
      "id": "1",
      "title": "CPU使用率过高",
      "description": "user-service CPU使用率超过80%，可能影响服务性能",
      "severity": "high",
      "source": "user-service",
      "status": "active",
      "createdAt": "2024-01-15T10:30:00Z",
      "resolvedAt": null
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 50,
    "totalPages": 3
  }
}
```

**字段说明:**
- `severity`: 严重级别 (low, medium, high, critical)
- `status`: 告警状态 (active, resolved)
- `source`: 告警来源服务
- `createdAt`: 创建时间
- `resolvedAt`: 解决时间 (仅已解决告警)

#### 创建告警
```http
POST /api/monitoring/alerts
```

**请求体:**
```json
{
  "title": "告警标题",
  "description": "告警描述",
  "severity": "high",
  "source": "service-name"
}
```

#### 解决告警
```http
PUT /api/monitoring/alerts/{id}/resolve
```

**请求体:**
```json
{
  "resolution": "解决方案描述"
}
```

### 4. 日志管理

#### 获取日志列表
```http
GET /api/monitoring/logs?search={search}&level={level}&service={service}&limit={limit}&offset={offset}
```

**请求参数:**
- `search`: 搜索关键词 - 可选
- `level`: 日志级别 (debug, info, warn, error) - 可选
- `service`: 服务名称 - 可选
- `limit`: 返回数量，默认100
- `offset`: 偏移量，默认0

**响应示例:**
```json
{
  "data": [
    {
      "id": "1",
      "timestamp": "2024-01-15T10:35:00Z",
      "level": "error",
      "service": "user-service",
      "source": "UserController",
      "message": "Failed to authenticate user: invalid token",
      "metadata": {
        "userId": "12345",
        "endpoint": "/api/users/profile",
        "statusCode": 401
      }
    }
  ],
  "pagination": {
    "limit": 100,
    "offset": 0,
    "total": 1500
  }
}
```

**字段说明:**
- `level`: 日志级别 (debug, info, warn, error, fatal)
- `service`: 产生日志的服务名称
- `source`: 日志来源 (类名、模块名等)
- `message`: 日志消息内容
- `metadata`: 附加元数据信息

#### 导出日志
```http
GET /api/monitoring/logs/export?format={format}&startTime={startTime}&endTime={endTime}
```

**请求参数:**
- `format`: 导出格式 (json, csv, txt)
- `startTime`: 开始时间 (ISO 8601格式)
- `endTime`: 结束时间 (ISO 8601格式)

**响应:**
返回对应格式的文件下载

## 数据类型定义

### MetricDataPoint
```typescript
interface MetricDataPoint {
  timestamp: string  // ISO 8601格式时间戳
  value: number      // 指标值
}
```

### SystemOverview
```typescript
interface SystemOverview {
  cpu: {
    current: number  // 当前值
    trend: number    // 趋势变化
  }
  memory: {
    current: number  // 当前使用量 (MB)
    total: number    // 总容量 (MB)
    trend: number    // 趋势变化 (MB)
  }
  network: {
    inbound: number  // 入站流量 (MB/s)
    outbound: number // 出站流量 (MB/s)
    trend: number    // 趋势变化 (MB/s)
  }
  alerts: {
    active: number   // 活跃告警数
    trend: number    // 趋势变化
  }
}
```

### Alert
```typescript
interface Alert {
  id: string
  title: string
  description: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  source: string
  status: 'active' | 'resolved'
  createdAt: string
  resolvedAt?: string
}
```

### LogEntry
```typescript
interface LogEntry {
  id: string
  timestamp: string
  level: 'debug' | 'info' | 'warn' | 'error' | 'fatal'
  service: string
  source: string
  message: string
  metadata: Record<string, any>
}
```

## 错误处理

### 标准错误响应
```json
{
  "error": {
    "code": "INVALID_PARAMETER",
    "message": "Invalid time range parameter",
    "details": {
      "parameter": "timeRange",
      "allowedValues": ["5m", "15m", "1h", "6h", "24h"]
    }
  }
}
```

### 常见错误码
- `INVALID_PARAMETER`: 参数错误
- `UNAUTHORIZED`: 未授权
- `FORBIDDEN`: 权限不足
- `NOT_FOUND`: 资源不存在
- `RATE_LIMITED`: 请求频率限制
- `INTERNAL_ERROR`: 服务器内部错误

## 实现说明

### 开发环境
- 使用 `mockMonitoringApi` 提供MOCK数据
- 自动生成时间序列数据，模拟真实场景
- 支持不同时间范围的数据生成

### 生产环境
- 使用 `monitoringApi` 调用真实API
- 通过环境变量配置API地址
- 支持完整的错误处理和重试机制

### 数据刷新
- 图表组件支持自动刷新 (默认30秒)
- 支持手动刷新所有图表
- 时间范围变化时自动重新获取数据

这套API设计确保了前端开发的完整性，同时为后端实现提供了清晰的接口规范。
