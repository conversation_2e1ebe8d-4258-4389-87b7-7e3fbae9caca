# DevOps 演示系统 - Vue3 版本

这是一个基于 Vue3 + TypeScript + Vite + Tailwind CSS 的 DevOps 平台演示系统，展示了微服务持续交付开发及容器化管理部署的完整流程。

## 功能特性

### 🚀 CI/CD 流水线
- 流水线可视化展示
- 实时执行状态监控
- 多阶段构建支持
- 日志查看和分析

### 📦 容器部署
- 应用容器化部署
- 多环境管理（开发/测试/预发/生产）
- 实例扩容缩容
- 健康检查监控

### 📊 监控中心
- 系统性能监控
- 告警管理
- 资源使用统计
- 实时指标展示

### 🌐 服务拓扑
- 微服务架构可视化
- 服务依赖关系图
- 服务状态监控
- 拓扑图交互操作

### 📈 系统总览
- 关键指标仪表板
- 部署趋势分析
- 资源使用情况
- 最近活动展示

## 技术栈

- **前端框架**: Vue 3.4+ (Composition API)
- **开发语言**: TypeScript 5.2+
- **构建工具**: Vite 5.0+
- **样式框架**: Tailwind CSS 3.3+
- **路由管理**: Vue Router 4.2+
- **状态管理**: Pinia 2.1+
- **图表库**: ECharts 5.4+ (vue-echarts)
- **图标库**: Lucide Vue Next
- **HTTP客户端**: Axios 1.6+
- **通知组件**: Vue Toastification

## 项目结构

```
src/
├── components/          # 组件目录
│   ├── common/         # 通用组件
│   ├── deployment/     # 部署相关组件
│   ├── monitoring/     # 监控相关组件
│   ├── pipeline/       # 流水线相关组件
│   └── topology/       # 拓扑相关组件
├── pages/              # 页面组件
├── router/             # 路由配置
├── stores/             # 状态管理
├── services/           # API服务
├── types/              # 类型定义
├── utils/              # 工具函数
└── styles/             # 样式文件
```

## 快速开始

### 环境要求

- Node.js 18.0+
- npm 9.0+ 或 yarn 1.22+ 或 pnpm 8.0+

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

访问 http://localhost:3000

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

### 代码检查

```bash
npm run lint
```

### 类型检查

```bash
npm run type-check
```

## 环境配置

复制 `.env.example` 为 `.env` 并根据需要修改配置：

```bash
cp .env.example .env
```

## 主要页面

- `/dashboard` - 系统总览
- `/pipeline` - CI/CD 流水线
- `/deployment` - 容器部署
- `/monitoring` - 监控中心
- `/topology` - 服务拓扑

## 开发说明

### 组件开发规范

1. 使用 Vue 3 Composition API
2. 使用 TypeScript 进行类型约束
3. 遵循单一职责原则
4. 组件命名使用 PascalCase
5. 事件命名使用 kebab-case

### 样式规范

1. 优先使用 Tailwind CSS 工具类
2. 自定义样式使用 CSS Modules 或 scoped
3. 响应式设计优先
4. 保持设计系统一致性

### 状态管理

使用 Pinia 进行状态管理，按功能模块划分 store：

- `useUserStore` - 用户状态
- `usePipelineStore` - 流水线状态
- `useApplicationStore` - 应用状态
- `useMonitoringStore` - 监控状态
- `useConfigStore` - 配置状态

## 部署

### Docker 部署

```bash
# 构建镜像
docker build -t devops-demo-vue .

# 运行容器
docker run -p 3000:80 devops-demo-vue
```

### Nginx 配置

```nginx
server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    location /api {
        proxy_pass http://backend:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 更新日志

### v1.0.0 (2024-01-15)

- ✨ 初始版本发布
- 🚀 完整的 DevOps 平台功能
- 📱 响应式设计支持
- 🎨 现代化 UI 界面
- 🔧 完善的开发工具链
