<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>服务拓扑测试</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
  <div style="padding: 20px;">
    <h1>服务拓扑测试页面</h1>
    
    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 20px;">
      <!-- 服务节点测试 -->
      <div>
        <h3>服务节点</h3>
        <div id="testTopology" style="position: relative; height: 300px; border: 1px solid #ccc; background: #f5f5f5;">
          <!-- 服务节点将在这里生成 -->
        </div>
      </div>
      
      <!-- 服务详情测试 -->
      <div>
        <h3>服务详情</h3>
        <div id="testServiceDetails" style="border: 1px solid #ccc; padding: 20px; height: 300px; overflow-y: auto;">
          <div style="text-align: center; padding: 40px 20px; color: var(--text-tertiary);">
            <i class="fas fa-mouse-pointer" style="font-size: 48px; margin-bottom: 16px;"></i>
            <p style="margin: 0; font-size: 14px;">点击服务节点查看详细信息</p>
          </div>
        </div>
      </div>
    </div>
    
    <div style="margin-top: 20px;">
      <button onclick="testSelectService('api-gateway')" style="margin-right: 10px;">测试选择 API Gateway</button>
      <button onclick="testSelectService('user-service')" style="margin-right: 10px;">测试选择 User Service</button>
      <button onclick="testSelectService('payment-service')">测试选择 Payment Service</button>
    </div>
  </div>

  <script>
    // 简化的服务数据
    const testServices = [
      {
        id: 'api-gateway',
        name: 'API Gateway',
        type: 'gateway',
        status: 'healthy',
        instances: 2,
        version: 'v1.0.0',
        cpu: 45,
        memory: 512,
        position: { x: 50, y: 50 },
        dependencies: ['user-service', 'payment-service']
      },
      {
        id: 'user-service',
        name: 'User Service',
        type: 'service',
        status: 'healthy',
        instances: 3,
        version: 'v1.2.3',
        cpu: 35,
        memory: 768,
        position: { x: 200, y: 50 },
        dependencies: []
      },
      {
        id: 'payment-service',
        name: 'Payment Service',
        type: 'service',
        status: 'unhealthy',
        instances: 2,
        version: 'v1.5.2',
        cpu: 85,
        memory: 1536,
        position: { x: 350, y: 50 },
        dependencies: []
      }
    ];

    // 获取服务状态颜色
    function getServiceStatusColor(status) {
      switch (status) {
        case 'healthy': return 'var(--success-color)';
        case 'unhealthy': return 'var(--danger-color)';
        case 'warning': return 'var(--warning-color)';
        default: return 'var(--text-tertiary)';
      }
    }

    // 获取服务类型图标
    function getServiceTypeIcon(type) {
      switch (type) {
        case 'gateway': return 'fas fa-door-open';
        case 'service': return 'fas fa-cube';
        case 'database': return 'fas fa-database';
        case 'middleware': return 'fas fa-cogs';
        case 'external': return 'fas fa-external-link-alt';
        default: return 'fas fa-question-circle';
      }
    }

    // 渲染测试拓扑
    function renderTestTopology() {
      const container = document.getElementById('testTopology');
      container.innerHTML = '';

      testServices.forEach(service => {
        const node = document.createElement('div');
        node.className = 'service-node';
        node.setAttribute('data-service-id', service.id);
        node.style.position = 'absolute';
        node.style.left = service.position.x + 'px';
        node.style.top = service.position.y + 'px';
        node.style.width = '80px';
        node.style.height = '80px';
        node.style.backgroundColor = 'white';
        node.style.border = `3px solid ${getServiceStatusColor(service.status)}`;
        node.style.borderRadius = '8px';
        node.style.padding = '8px';
        node.style.cursor = 'pointer';
        node.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
        node.style.transition = 'all 0.2s';
        node.style.display = 'flex';
        node.style.flexDirection = 'column';
        node.style.alignItems = 'center';
        node.style.justifyContent = 'center';
        node.style.textAlign = 'center';

        node.innerHTML = `
          <i class="${getServiceTypeIcon(service.type)}" style="font-size: 20px; color: ${getServiceStatusColor(service.status)}; margin-bottom: 4px;"></i>
          <div style="font-size: 9px; font-weight: 600; color: var(--text-primary); line-height: 1.2; word-break: break-word;">
            ${service.name}
          </div>
        `;

        node.addEventListener('click', () => testSelectService(service.id));
        container.appendChild(node);
      });
    }

    // 渲染服务详情
    function renderTestServiceDetails(service) {
      const container = document.getElementById('testServiceDetails');
      console.log('渲染服务详情:', service);
      
      container.innerHTML = `
        <div style="border-left: 3px solid ${getServiceStatusColor(service.status)}; padding-left: 12px; margin-bottom: 16px;">
          <h4 style="margin: 0 0 4px 0; font-size: 16px; font-weight: 600; color: var(--text-primary);">
            ${service.name}
          </h4>
          <div style="font-size: 12px; color: var(--text-secondary);">
            ${service.type.charAt(0).toUpperCase() + service.type.slice(1)} • ${service.version}
          </div>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-bottom: 16px;">
          <div style="text-align: center; padding: 8px; background-color: var(--bg-color); border-radius: 4px;">
            <div style="font-size: 18px; font-weight: 600; color: var(--text-primary);">${service.instances}</div>
            <div style="font-size: 12px; color: var(--text-secondary);">实例数</div>
          </div>
          <div style="text-align: center; padding: 8px; background-color: var(--bg-color); border-radius: 4px;">
            <div style="font-size: 18px; font-weight: 600; color: ${getServiceStatusColor(service.status)};">
              ${service.status === 'healthy' ? '健康' : service.status === 'unhealthy' ? '异常' : '警告'}
            </div>
            <div style="font-size: 12px; color: var(--text-secondary);">状态</div>
          </div>
        </div>

        <div style="margin-bottom: 16px;">
          <h5 style="margin: 0 0 8px 0; font-size: 14px; font-weight: 600; color: var(--text-primary);">资源使用</h5>
          <div style="margin-bottom: 8px;">
            <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
              <span style="font-size: 12px; color: var(--text-secondary);">CPU</span>
              <span style="font-size: 12px; color: var(--text-primary);">${service.cpu}%</span>
            </div>
            <div style="width: 100%; height: 6px; background-color: var(--bg-color); border-radius: 3px; overflow: hidden;">
              <div style="height: 100%; background-color: ${service.cpu > 80 ? 'var(--danger-color)' : service.cpu > 60 ? 'var(--warning-color)' : 'var(--success-color)'}; width: ${service.cpu}%;"></div>
            </div>
          </div>
          <div>
            <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
              <span style="font-size: 12px; color: var(--text-secondary);">内存</span>
              <span style="font-size: 12px; color: var(--text-primary);">${service.memory}MB</span>
            </div>
            <div style="width: 100%; height: 6px; background-color: var(--bg-color); border-radius: 3px; overflow: hidden;">
              <div style="height: 100%; background-color: ${service.memory > 1500 ? 'var(--danger-color)' : service.memory > 1000 ? 'var(--warning-color)' : 'var(--success-color)'}; width: ${Math.min(100, (service.memory / 2048) * 100)}%;"></div>
            </div>
          </div>
        </div>

        ${service.dependencies.length > 0 ? `
          <div>
            <h5 style="margin: 0 0 8px 0; font-size: 14px; font-weight: 600; color: var(--text-primary);">依赖服务</h5>
            <div style="display: flex; flex-direction: column; gap: 4px;">
              ${service.dependencies.map(depId => {
                const depService = testServices.find(s => s.id === depId);
                return depService ? `
                  <div style="display: flex; align-items: center; gap: 8px; padding: 4px 8px; background-color: var(--bg-color); border-radius: 4px; cursor: pointer;" onclick="testSelectService('${depService.id}')">
                    <i class="${getServiceTypeIcon(depService.type)}" style="color: ${getServiceStatusColor(depService.status)}; font-size: 12px;"></i>
                    <span style="font-size: 12px; color: var(--text-primary);">${depService.name}</span>
                  </div>
                ` : '';
              }).join('')}
            </div>
          </div>
        ` : ''}
      `;
    }

    // 测试选择服务
    function testSelectService(serviceId) {
      console.log('选择服务:', serviceId);
      const service = testServices.find(s => s.id === serviceId);
      if (service) {
        renderTestServiceDetails(service);
        
        // 高亮选中的节点
        document.querySelectorAll('.service-node').forEach(node => {
          node.style.borderWidth = '3px';
          node.classList.remove('selected');
        });
        
        const selectedNode = document.querySelector(`[data-service-id="${serviceId}"]`);
        if (selectedNode) {
          selectedNode.style.borderWidth = '4px';
          selectedNode.classList.add('selected');
        }
      } else {
        console.error('未找到服务:', serviceId);
      }
    }

    // 初始化
    document.addEventListener('DOMContentLoaded', function() {
      console.log('页面加载完成，初始化测试拓扑');
      renderTestTopology();
    });
  </script>
</body>
</html>
