<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>数智化运营平台 - 离线采集任务管理</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <i class="fas fa-chart-line"></i> 数智化运营平台
      </div>
      <div style="display: flex; align-items: center;">
        <div class="dropdown" style="margin-right: 16px;">
          <button class="dropdown-toggle">
            <i class="fas fa-bell"></i>
            <span class="badge">3</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item">
              <div style="font-weight: 500;">新任务通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">您有3个新任务需要处理</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">数据采集完成</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">昨日数据采集已完成</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">系统更新</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">平台将于今晚23:00进行维护</div>
            </div>
          </div>
        </div>
        <div class="dropdown">
          <button class="dropdown-toggle">
            <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px;">
            <span>管理员</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item"><i class="fas fa-user"></i> 个人中心</div>
            <div class="dropdown-item"><i class="fas fa-cog"></i> 系统设置</div>
            <div class="dropdown-item"><i class="fas fa-sign-out-alt"></i> 退出登录</div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- 侧边栏 -->
  <div class="sidebar">
    <div class="menu-item">
      <i class="fas fa-home menu-icon"></i>
      <span class="menu-text">首页</span>
    </div>
    <div class="menu-item active">
      <i class="fas fa-database menu-icon"></i>
      <span class="menu-text">数据融通</span>
      <div style="margin-left: 24px; margin-top: 8px; font-size: 14px;">
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">数据源管理</div>
        <div style="padding: 8px 0; color: var(--primary-color); cursor: pointer;">离线采集任务管理</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">实时采集任务管理</div>
      </div>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-pie menu-icon"></i>
      <span class="menu-text">智能洞察分析</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-bar menu-icon"></i>
      <span class="menu-text">运营视图</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tachometer-alt menu-icon"></i>
      <span class="menu-text">统一运营门户</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tasks menu-icon"></i>
      <span class="menu-text">五级穿透调度</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-server menu-icon"></i>
      <span class="menu-text">微服务管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-user-shield menu-icon"></i>
      <span class="menu-text">权限管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-cog menu-icon"></i>
      <span class="menu-text">系统设置</span>
    </div>
  </div>

  <!-- 主内容区 -->
  <div class="main-content">
    <div class="page-title">
      <i class="fas fa-clock page-title-icon"></i>
      离线采集任务管理
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit;">首页</a></div>
      <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit;">数据融通</a></div>
      <div class="breadcrumb-item active">离线采集任务管理</div>
    </div>

    <!-- 搜索和操作栏 -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
      <div class="search-box" style="width: 300px; margin-bottom: 0;">
        <i class="fas fa-search search-box-icon"></i>
        <input type="text" placeholder="搜索任务...">
      </div>
      <div style="display: flex;">
        <div style="margin-right: 12px;">
          <select style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
            <option value="all">全部状态</option>
            <option value="running">运行中</option>
            <option value="stopped">已停止</option>
            <option value="completed">已完成</option>
            <option value="failed">已失败</option>
            <option value="waiting">等待执行</option>
          </select>
        </div>
        <button class="btn btn-primary" data-modal-target="addOfflineTaskModal"><i class="fas fa-plus"></i> 新增任务</button>
      </div>
    </div>

    <!-- 任务列表表格 -->
    <div class="card">
      <div class="table-container">
        <table class="table">
          <thead>
            <tr>
              <th>任务名称</th>
              <th>数据源</th>
              <th>目标表</th>
              <th>调度周期</th>
              <th>状态</th>
              <th>创建时间</th>
              <th>最近执行时间</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>用户数据同步</td>
              <td>MySQL-用户数据库</td>
              <td>dw.user_info</td>
              <td>每天 00:30</td>
              <td><span class="tag tag-success">已完成</span></td>
              <td>2023-07-01 15:30</td>
              <td>2023-07-15 00:30</td>
              <td>
                <button class="btn" style="color: var(--primary-color);" data-modal-target="editOfflineTaskModal"><i class="fas fa-edit"></i></button>
                <button class="btn" style="color: var(--primary-color);"><i class="fas fa-play"></i></button>
                <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
                <button class="btn" style="color: var(--primary-color);"><i class="fas fa-history"></i></button>
              </td>
            </tr>
            <tr>
              <td>销售数据同步</td>
              <td>Oracle-销售数据库</td>
              <td>dw.sales_info</td>
              <td>每天 01:00</td>
              <td><span class="tag tag-success">已完成</span></td>
              <td>2023-07-02 10:15</td>
              <td>2023-07-15 01:00</td>
              <td>
                <button class="btn" style="color: var(--primary-color);" data-modal-target="editOfflineTaskModal"><i class="fas fa-edit"></i></button>
                <button class="btn" style="color: var(--primary-color);"><i class="fas fa-play"></i></button>
                <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
                <button class="btn" style="color: var(--primary-color);"><i class="fas fa-history"></i></button>
              </td>
            </tr>
            <tr>
              <td>日志数据导入</td>
              <td>文件-日志文件</td>
              <td>dw.log_info</td>
              <td>每小时</td>
              <td><span class="tag tag-danger">已失败</span></td>
              <td>2023-07-03 14:45</td>
              <td>2023-07-15 09:00</td>
              <td>
                <button class="btn" style="color: var(--primary-color);" data-modal-target="editOfflineTaskModal"><i class="fas fa-edit"></i></button>
                <button class="btn" style="color: var(--primary-color);"><i class="fas fa-redo"></i></button>
                <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
                <button class="btn" style="color: var(--primary-color);"><i class="fas fa-history"></i></button>
              </td>
            </tr>
            <tr>
              <td>报表数据同步</td>
              <td>FTP-报表文件</td>
              <td>dw.report_info</td>
              <td>每周一 02:00</td>
              <td><span class="tag tag-warning">等待执行</span></td>
              <td>2023-07-05 09:20</td>
              <td>2023-07-10 02:00</td>
              <td>
                <button class="btn" style="color: var(--primary-color);" data-modal-target="editOfflineTaskModal"><i class="fas fa-edit"></i></button>
                <button class="btn" style="color: var(--warning-color);"><i class="fas fa-pause"></i></button>
                <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
                <button class="btn" style="color: var(--primary-color);"><i class="fas fa-history"></i></button>
              </td>
            </tr>
            <tr>
              <td>商品数据同步</td>
              <td>API-第三方API</td>
              <td>dw.product_info</td>
              <td>每天 03:00</td>
              <td><span class="tag tag-success">已完成</span></td>
              <td>2023-07-08 16:05</td>
              <td>2023-07-15 03:00</td>
              <td>
                <button class="btn" style="color: var(--primary-color);" data-modal-target="editOfflineTaskModal"><i class="fas fa-edit"></i></button>
                <button class="btn" style="color: var(--primary-color);"><i class="fas fa-play"></i></button>
                <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
                <button class="btn" style="color: var(--primary-color);"><i class="fas fa-history"></i></button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="pagination">
        <div class="pagination-item"><i class="fas fa-chevron-left"></i></div>
        <div class="pagination-item active">1</div>
        <div class="pagination-item">2</div>
        <div class="pagination-item">3</div>
        <div class="pagination-item">4</div>
        <div class="pagination-item">5</div>
        <div class="pagination-item"><i class="fas fa-chevron-right"></i></div>
      </div>
    </div>
  </div>

  <!-- 新增离线任务模态框 -->
  <div class="modal" id="addOfflineTaskModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-plus"></i> 新增离线采集任务</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <form id="addOfflineTaskForm">
          <div class="form-group">
            <label for="taskName">任务名称</label>
            <input type="text" id="taskName" name="taskName" required placeholder="请输入任务名称">
          </div>
          <div class="form-group">
            <label for="dataSource">数据源</label>
            <select id="dataSource" name="dataSource" required>
              <option value="">请选择数据源</option>
              <option value="mysql_user">MySQL-用户数据库</option>
              <option value="oracle_sales">Oracle-销售数据库</option>
              <option value="file_log">文件-日志文件</option>
              <option value="ftp_report">FTP-报表文件</option>
              <option value="api_third">API-第三方API</option>
            </select>
          </div>
          <div class="form-group">
            <label for="targetTable">目标表</label>
            <input type="text" id="targetTable" name="targetTable" required placeholder="请输入目标表名">
          </div>
          <div class="form-group">
            <label for="scheduleType">调度类型</label>
            <select id="scheduleType" name="scheduleType" required onchange="changeScheduleForm(this.value)">
              <option value="">请选择调度类型</option>
              <option value="daily">每天</option>
              <option value="weekly">每周</option>
              <option value="monthly">每月</option>
              <option value="hourly">每小时</option>
              <option value="custom">自定义</option>
            </select>
          </div>
          <div id="dailySchedule" style="display: none;">
            <div class="form-group">
              <label for="dailyTime">执行时间</label>
              <input type="time" id="dailyTime" name="dailyTime" required>
            </div>
          </div>
          <div id="weeklySchedule" style="display: none;">
            <div class="form-group">
              <label>执行日期</label>
              <div style="display: flex; flex-wrap: wrap;">
                <label style="display: flex; align-items: center; margin-right: 16px; margin-bottom: 8px;">
                  <input type="checkbox" name="weeklyDays" value="1"> 周一
                </label>
                <label style="display: flex; align-items: center; margin-right: 16px; margin-bottom: 8px;">
                  <input type="checkbox" name="weeklyDays" value="2"> 周二
                </label>
                <label style="display: flex; align-items: center; margin-right: 16px; margin-bottom: 8px;">
                  <input type="checkbox" name="weeklyDays" value="3"> 周三
                </label>
                <label style="display: flex; align-items: center; margin-right: 16px; margin-bottom: 8px;">
                  <input type="checkbox" name="weeklyDays" value="4"> 周四
                </label>
                <label style="display: flex; align-items: center; margin-right: 16px; margin-bottom: 8px;">
                  <input type="checkbox" name="weeklyDays" value="5"> 周五
                </label>
                <label style="display: flex; align-items: center; margin-right: 16px; margin-bottom: 8px;">
                  <input type="checkbox" name="weeklyDays" value="6"> 周六
                </label>
                <label style="display: flex; align-items: center; margin-bottom: 8px;">
                  <input type="checkbox" name="weeklyDays" value="0"> 周日
                </label>
              </div>
            </div>
            <div class="form-group">
              <label for="weeklyTime">执行时间</label>
              <input type="time" id="weeklyTime" name="weeklyTime" required>
            </div>
          </div>
          <div id="monthlySchedule" style="display: none;">
            <div class="form-group">
              <label for="monthlyDay">执行日期</label>
              <input type="number" id="monthlyDay" name="monthlyDay" min="1" max="31" required placeholder="请输入日期">
            </div>
            <div class="form-group">
              <label for="monthlyTime">执行时间</label>
              <input type="time" id="monthlyTime" name="monthlyTime" required>
            </div>
          </div>
          <div id="hourlySchedule" style="display: none;">
            <div class="form-group">
              <label for="hourlyInterval">执行间隔</label>
              <input type="number" id="hourlyInterval" name="hourlyInterval" min="1" max="24" required placeholder="请输入间隔小时数">
            </div>
          </div>
          <div id="customSchedule" style="display: none;">
            <div class="form-group">
              <label for="cronExpression">Cron表达式</label>
              <input type="text" id="cronExpression" name="cronExpression" required placeholder="请输入Cron表达式">
              <div style="font-size: 12px; color: var(--text-tertiary); margin-top: 4px;">格式: 秒 分 时 日 月 周 年</div>
            </div>
          </div>
          <div class="form-group">
            <label for="dataPreprocess">数据预处理</label>
            <textarea id="dataPreprocess" name="dataPreprocess" rows="3" placeholder="请输入数据预处理SQL或脚本"></textarea>
          </div>
          <div class="form-group">
            <label for="securityLevel">安全级别</label>
            <select id="securityLevel" name="securityLevel">
              <option value="low">低</option>
              <option value="medium" selected>中</option>
              <option value="high">高</option>
            </select>
          </div>
          <div class="form-group">
            <label for="taskDescription">任务描述</label>
            <textarea id="taskDescription" name="taskDescription" rows="3" placeholder="请输入任务描述"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('addOfflineTaskModal').classList.remove('show')">取消</button>
        <button class="btn btn-primary" onclick="document.getElementById('addOfflineTaskForm').submit()">保存并上线</button>
      </div>
    </div>
  </div>

  <!-- 编辑离线任务模态框 -->
  <div class="modal" id="editOfflineTaskModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-edit"></i> 编辑离线采集任务</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <!-- 编辑表单内容与新增表单类似，这里省略 -->
        <div style="text-align: center; padding: 20px;">
          <p>编辑离线采集任务表单内容与新增表单类似，实际应用中会加载任务的当前配置。</p>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('editOfflineTaskModal').classList.remove('show')">取消</button>
        <button class="btn btn-primary">保存修改</button>
      </div>
    </div>
  </div>

  <script src="js/common.js"></script>
  <script>
    // 切换调度类型表单
    function changeScheduleForm(type) {
      // 隐藏所有调度表单
      document.getElementById('dailySchedule').style.display = 'none';
      document.getElementById('weeklySchedule').style.display = 'none';
      document.getElementById('monthlySchedule').style.display = 'none';
      document.getElementById('hourlySchedule').style.display = 'none';
      document.getElementById('customSchedule').style.display = 'none';

      // 显示选中的调度表单
      if (type) {
        document.getElementById(type + 'Schedule').style.display = 'block';
      }
    }

    // 新增离线任务表单提交
    document.getElementById('addOfflineTaskForm').addEventListener('submit', function(e) {
      e.preventDefault();
      if (validateForm('addOfflineTaskForm')) {
        // 模拟提交成功
        alert('离线采集任务创建成功并已上线！');
        document.getElementById('addOfflineTaskModal').classList.remove('show');
        // 重置表单
        this.reset();
        changeScheduleForm('');
      }
    });
  </script>
</body>
</html>