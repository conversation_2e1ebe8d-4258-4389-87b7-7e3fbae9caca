<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>数智化运营平台 - 五级穿透管理</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <i class="fas fa-chart-line"></i> 数智化运营平台
      </div>
      <div style="display: flex; align-items: center;">
        <div class="dropdown" style="margin-right: 16px;">
          <button class="dropdown-toggle">
            <i class="fas fa-bell"></i>
            <span class="badge">3</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item">
              <div style="font-weight: 500;">穿透配置更新</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">全国-省份穿透配置已更新</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">穿透测试通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">任务#2001测试成功</div>
            </div>
          </div>
        </div>
        <div class="dropdown">
          <button class="dropdown-toggle">
            <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px;">
            <span>管理员</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item"><i class="fas fa-user"></i> 个人中心</div>
            <div class="dropdown-item"><i class="fas fa-cog"></i> 系统设置</div>
            <div class="dropdown-item"><i class="fas fa-sign-out-alt"></i> 退出登录</div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- 侧边栏 -->
  <div class="sidebar">
    <div class="menu-item">
      <i class="fas fa-home menu-icon"></i>
      <span class="menu-text">首页</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-database menu-icon"></i>
      <span class="menu-text">数据融通</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-pie menu-icon"></i>
      <span class="menu-text">智能洞察分析</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-bar menu-icon"></i>
      <span class="menu-text">运营视图</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tachometer-alt menu-icon"></i>
      <span class="menu-text">统一运营门户</span>
    </div>
    <div class="menu-item active">
      <i class="fas fa-tasks menu-icon"></i>
      <span class="menu-text">五级穿透调度</span>
      <div style="margin-left: 24px; margin-top: 8px; font-size: 14px;">
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">任务调度</div>
        <div style="padding: 8px 0; color: var(--primary-color); cursor: pointer;">五级穿透</div>
      </div>
    </div>
    <div class="menu-item">
      <i class="fas fa-server menu-icon"></i>
      <span class="menu-text">微服务管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-user-shield menu-icon"></i>
      <span class="menu-text">权限管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-cog menu-icon"></i>
      <span class="menu-text">系统设置</span>
    </div>
  </div>

  <!-- 主内容区 -->
  <div class="main-content">
    <div class="page-title">
      <i class="fas fa-project-diagram page-title-icon"></i>
      五级穿透管理
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit;">首页</a></div>
      <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit;">五级穿透调度</a></div>
      <div class="breadcrumb-item active">五级穿透</div>
    </div>

    <!-- 穿透层级可视化 -->
    <div class="card" style="margin-bottom: 20px;">
      <div class="card-header">
        <div class="card-title">穿透层级可视化</div>
      </div>
      <div class="card-body" style="display: flex; justify-content: center; align-items: center; height: 200px;">
        <div class="penetration-diagram">
          <div class="level-box level-1">
            <div class="level-title">全国</div>
          </div>
          <div class="level-connector"><i class="fas fa-chevron-down"></i></div>
          <div class="level-box level-2">
            <div class="level-title">省份</div>
          </div>
          <div class="level-connector"><i class="fas fa-chevron-down"></i></div>
          <div class="level-box level-3">
            <div class="level-title">城市</div>
          </div>
          <div class="level-connector"><i class="fas fa-chevron-down"></i></div>
          <div class="level-box level-4">
            <div class="level-title">区县</div>
          </div>
          <div class="level-connector"><i class="fas fa-chevron-down"></i></div>
          <div class="level-box level-5">
            <div class="level-title">具体业务</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索和操作栏 -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
      <div style="display: flex; width: 60%;">
        <div class="search-box" style="width: 300px; margin-bottom: 0; margin-right: 12px;">
          <i class="fas fa-search search-box-icon"></i>
          <input type="text" placeholder="搜索穿透配置...">
        </div>
        <div style="margin-right: 12px;">
          <select style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
            <option value="all">全部层级</option>
            <option value="1">全国</option>
            <option value="2">省份</option>
            <option value="3">城市</option>
            <option value="4">区县</option>
            <option value="5">具体业务</option>
          </select>
        </div>
      </div>
      <div style="display: flex;">
        <button class="btn" style="border: 1px solid var(--border-color); margin-right: 12px;"><i class="fas fa-filter"></i> 高级筛选</button>
        <button class="btn btn-primary" data-modal-target="addPenetrationModal"><i class="fas fa-plus"></i> 新增穿透配置</button>
      </div>
    </div>

    <!-- 穿透配置列表 -->
    <div class="card">
      <div class="table-container">
        <table class="table">
          <thead>
            <tr>
              <th>配置ID</th>
              <th>穿透名称</th>
              <th>源层级</th>
              <th>目标层级</th>
              <th>源页面</th>
              <th>目标页面</th>
              <th>参数映射</th>
              <th>状态</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>#2001</td>
              <td>全国-省份穿透</td>
              <td>全国</td>
              <td>省份</td>
              <td>全国数据大屏</td>
              <td>省份数据大屏</td>
              <td>provinceCode=code</td>
              <td><span class="tag tag-success">启用</span></td>
              <td>
                <button class="btn" style="color: var(--primary-color);"><i class="fas fa-eye"></i></button>
                <button class="btn" style="color: var(--primary-color);" data-modal-target="editPenetrationModal"><i class="fas fa-edit"></i></button>
                <button class="btn" style="color: var(--warning-color);"><i class="fas fa-power-off"></i></button>
              </td>
            </tr>
            <tr>
              <td>#2002</td>
              <td>省份-城市穿透</td>
              <td>省份</td>
              <td>城市</td>
              <td>省份数据大屏</td>
              <td>城市数据大屏</td>
              <td>cityCode=code</td>
              <td><span class="tag tag-success">启用</span></td>
              <td>
                <button class="btn" style="color: var(--primary-color);"><i class="fas fa-eye"></i></button>
                <button class="btn" style="color: var(--primary-color);" data-modal-target="editPenetrationModal"><i class="fas fa-edit"></i></button>
                <button class="btn" style="color: var(--warning-color);"><i class="fas fa-power-off"></i></button>
              </td>
            </tr>
            <tr>
              <td>#2003</td>
              <td>城市-区县穿透</td>
              <td>城市</td>
              <td>区县</td>
              <td>城市数据大屏</td>
              <td>区县数据大屏</td>
              <td>districtCode=code</td>
              <td><span class="tag tag-success">启用</span></td>
              <td>
                <button class="btn" style="color: var(--primary-color);"><i class="fas fa-eye"></i></button>
                <button class="btn" style="color: var(--primary-color);" data-modal-target="editPenetrationModal"><i class="fas fa-edit"></i></button>
                <button class="btn" style="color: var(--warning-color);"><i class="fas fa-power-off"></i></button>
              </td>
            </tr>
            <tr>
              <td>#2004</td>
              <td>区县-业务穿透</td>
              <td>区县</td>
              <td>具体业务</td>
              <td>区县数据大屏</td>
              <td>业务详情页面</td>
              <td>businessId=id</td>
              <td><span class="tag tag-danger">禁用</span></td>
              <td>
                <button class="btn" style="color: var(--primary-color);"><i class="fas fa-eye"></i></button>
                <button class="btn" style="color: var(--primary-color);" data-modal-target="editPenetrationModal"><i class="fas fa-edit"></i></button>
                <button class="btn" style="color: var(--success-color);"><i class="fas fa-power-off"></i></button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="pagination">
        <div class="pagination-item"><i class="fas fa-chevron-left"></i></div>
        <div class="pagination-item active">1</div>
        <div class="pagination-item">2</div>
        <div class="pagination-item">3</div>
        <div class="pagination-item"><i class="fas fa-chevron-right"></i></div>
      </div>
    </div>
  </div>

  <!-- 新增穿透配置模态框 -->
  <div class="modal" id="addPenetrationModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-plus"></i> 新增穿透配置</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <form id="addPenetrationForm">
          <div class="form-group">
            <label for="penetrationName">穿透名称</label>
            <input type="text" id="penetrationName" name="penetrationName" required placeholder="请输入穿透名称">
          </div>
          <div class="form-group">
            <label for="sourceLevel">源层级</label>
            <select id="sourceLevel" name="sourceLevel" required>
              <option value="">请选择源层级</option>
              <option value="1">全国</option>
              <option value="2">省份</option>
              <option value="3">城市</option>
              <option value="4">区县</option>
              <option value="5">具体业务</option>
            </select>
          </div>
          <div class="form-group">
            <label for="targetLevel">目标层级</label>
            <select id="targetLevel" name="targetLevel" required>
              <option value="">请选择目标层级</option>
              <option value="1">全国</option>
              <option value="2">省份</option>
              <option value="3">城市</option>
              <option value="4">区县</option>
              <option value="5">具体业务</option>
            </select>
          </div>
          <div class="form-group">
            <label for="sourcePage">源页面</label>
            <select id="sourcePage" name="sourcePage" required>
              <option value="">请选择源页面</option>
              <option value="national_dashboard">全国数据大屏</option>
              <option value="province_dashboard">省份数据大屏</option>
              <option value="city_dashboard">城市数据大屏</option>
              <option value="district_dashboard">区县数据大屏</option>
              <option value="business_detail">业务详情页面</option>
            </select>
          </div>
          <div class="form-group">
            <label for="targetPage">目标页面</label>
            <select id="targetPage" name="targetPage" required>
              <option value="">请选择目标页面</option>
              <option value="national_dashboard">全国数据大屏</option>
              <option value="province_dashboard">省份数据大屏</option>
              <option value="city_dashboard">城市数据大屏</option>
              <option value="district_dashboard">区县数据大屏</option>
              <option value="business_detail">业务详情页面</option>
            </select>
          </div>
          <div class="form-group">
            <label for="paramMapping">参数映射</label>
            <input type="text" id="paramMapping" name="paramMapping" required placeholder="例如：targetParam=sourceParam">
          </div>
          <div class="form-group">
            <label for="description">描述</label>
            <textarea id="description" name="description" rows="3" placeholder="请输入穿透配置描述"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('addPenetrationModal').classList.remove('show')">取消</button>
        <button class="btn btn-primary" onclick="document.getElementById('addPenetrationForm').submit()">创建配置</button>
      </div>
    </div>
  </div>

  <!-- 编辑穿透配置模态框 -->
  <div class="modal" id="editPenetrationModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-edit"></i> 编辑穿透配置</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <!-- 编辑表单内容与新增表单类似，这里省略 -->
        <div style="text-align: center; padding: 20px;">
          <p>编辑穿透配置表单内容与新增表单类似，实际应用中会加载配置的当前信息。</p>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('editPenetrationModal').classList.remove('show')">取消</button>
        <button class="btn btn-primary">保存修改</button>
      </div>
    </div>
  </div>

  <script src="js/common.js"></script>
  <script>
    // 新增穿透配置表单提交
    document.getElementById('addPenetrationForm').addEventListener('submit', function(e) {
      e.preventDefault();
      if (validateForm('addPenetrationForm')) {
        // 模拟提交成功
        alert('穿透配置创建成功！');
        document.getElementById('addPenetrationModal').classList.remove('show');
        // 重置表单
        this.reset();
      }
    });

    // 为穿透层级添加特殊样式
    document.addEventListener('DOMContentLoaded', function() {
      // 穿透层级可视化样式
      const levelBoxes = document.querySelectorAll('.level-box');
      levelBoxes.forEach((box, index) => {
        box.style.margin = '0 10px';
        box.style.padding = '15px 25px';
        box.style.borderRadius = '8px';
        box.style.textAlign = 'center';
        box.style.fontWeight = 'bold';
        box.style.color = '#fff';
        box.style.backgroundColor = `var(--level-${index + 1}-color)`;
      });

      const connectors = document.querySelectorAll('.level-connector');
      connectors.forEach(connector => {
        connector.style.margin = '5px 0';
        connector.style.textAlign = 'center';
        connector.style.color = 'var(--primary-color)';
      });

      document.querySelector('.penetration-diagram').style.display = 'flex';
      document.querySelector('.penetration-diagram').style.flexDirection = 'column';
      document.querySelector('.penetration-diagram').style.alignItems = 'center';
    });
  </script>
</body>
</html>