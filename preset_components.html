<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>数智化运营平台 - 预制组件</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <i class="fas fa-chart-line"></i> 数智化运营平台
      </div>
      <div style="display: flex; align-items: center;">
        <div class="dropdown" style="margin-right: 16px;">
          <button class="dropdown-toggle">
            <i class="fas fa-bell"></i>
            <span class="badge">3</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item">
              <div style="font-weight: 500;">新任务通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">您有3个新任务需要处理</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">数据采集完成</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">昨日数据采集已完成</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">系统更新</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">平台将于今晚23:00进行维护</div>
            </div>
          </div>
        </div>
        <div class="dropdown">
          <button class="dropdown-toggle">
            <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px;">
            <span>管理员</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item"><i class="fas fa-user"></i> 个人中心</div>
            <div class="dropdown-item"><i class="fas fa-cog"></i> 系统设置</div>
            <div class="dropdown-item"><i class="fas fa-sign-out-alt"></i> 退出登录</div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- 侧边栏 -->
  <div class="sidebar">
    <div class="menu-item">
      <i class="fas fa-home menu-icon"></i>
      <span class="menu-text">首页</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-database menu-icon"></i>
      <span class="menu-text">数据融通</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-pie menu-icon"></i>
      <span class="menu-text">智能洞察分析</span>
    </div>
    <div class="menu-item active">
      <i class="fas fa-chart-bar menu-icon"></i>
      <span class="menu-text">运营视图</span>
      <div style="margin-left: 24px; margin-top: 8px; font-size: 14px;">
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">预置大屏模板</div>
        <div style="padding: 8px 0; color: var(--primary-color); cursor: pointer;">预制组件</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">模板管理</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">画布管理</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">自定义报表</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">视图操作能力</div>
      </div>
    </div>
    <div class="menu-item">
      <i class="fas fa-tachometer-alt menu-icon"></i>
      <span class="menu-text">统一运营门户</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tasks menu-icon"></i>
      <span class="menu-text">五级穿透调度</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-server menu-icon"></i>
      <span class="menu-text">微服务管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-user-shield menu-icon"></i>
      <span class="menu-text">权限管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-cog menu-icon"></i>
      <span class="menu-text">系统设置</span>
    </div>
  </div>

  <!-- 主内容区 -->
  <div class="main-content">
    <div class="page-title">
      <i class="fas fa-puzzle-piece page-title-icon"></i>
      运营视图 - 预制组件
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit;">首页</a></div>
      <div class="breadcrumb-item"><a href="operation_views.html" style="text-decoration: none; color: inherit;">运营视图</a></div>
      <div class="breadcrumb-item active">预制组件</div>
    </div>

    <!-- 筛选和操作栏 -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
      <div style="display: flex;">
        <div style="margin-right: 12px;">
          <select style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
            <option value="all">全部组件</option>
            <option value="chart">图表组件</option>
            <option value="table">表格组件</option>
            <option value="map">地图组件</option>
            <option value="card">卡片组件</option>
          </select>
        </div>
        <div>
          <select style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
            <option value="all">全部行业</option>
            <option value="retail">零售行业</option>
            <option value="finance">金融行业</option>
            <option value="manufacturing">制造业</option>
            <option value="service">服务业</option>
          </select>
        </div>
      </div>
      <div style="display: flex;">
        <button class="btn" style="border: 1px solid var(--border-color); margin-right: 12px;"><i class="fas fa-filter"></i> 筛选</button>
        <button class="btn btn-primary" data-modal-target="createComponentModal"><i class="fas fa-plus"></i> 创建组件</button>
      </div>
    </div>

    <!-- 组件列表 -->
    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 24px;">
      <!-- 折线图组件 -->
      <div class="card" style="height: 300px; display: flex; flex-direction: column;">
        <div style="padding: 16px; border-bottom: 1px solid var(--border-color);">
          <div style="font-size: 16px; font-weight: 500; display: flex; align-items: center;">
            <i class="fas fa-chart-line" style="margin-right: 8px; color: var(--primary-color);"></i> 折线图组件
          </div>
          <div style="font-size: 12px; color: var(--text-tertiary); margin-top: 4px;">用于展示数据趋势变化</div>
        </div>
        <div style="flex-grow: 1; display: flex; justify-content: center; align-items: center; padding: 16px;">
          <div style="width: 100%; height: 160px; background-color: var(--bg-secondary); border-radius: 4px; display: flex; justify-content: center; align-items: center;">
            <i class="fas fa-chart-line" style="font-size: 48px; color: var(--text-tertiary);"></i>
          </div>
        </div>
        <div style="padding: 16px; border-top: 1px solid var(--border-color); display: flex; justify-content: flex-end;">
          <button class="btn" style="color: var(--primary-color); margin-right: 8px;"><i class="fas fa-edit"></i> 编辑</button>
          <button class="btn btn-primary"><i class="fas fa-plus-circle"></i> 添加</button>
        </div>
      </div>

      <!-- 柱状图组件 -->
      <div class="card" style="height: 300px; display: flex; flex-direction: column;">
        <div style="padding: 16px; border-bottom: 1px solid var(--border-color);">
          <div style="font-size: 16px; font-weight: 500; display: flex; align-items: center;">
            <i class="fas fa-chart-bar" style="margin-right: 8px; color: var(--primary-color);"></i> 柱状图组件
          </div>
          <div style="font-size: 12px; color: var(--text-tertiary); margin-top: 4px;">用于对比不同类别数据</div>
        </div>
        <div style="flex-grow: 1; display: flex; justify-content: center; align-items: center; padding: 16px;">
          <div style="width: 100%; height: 160px; background-color: var(--bg-secondary); border-radius: 4px; display: flex; justify-content: center; align-items: center;">
            <i class="fas fa-chart-bar" style="font-size: 48px; color: var(--text-tertiary);"></i>
          </div>
        </div>
        <div style="padding: 16px; border-top: 1px solid var(--border-color); display: flex; justify-content: flex-end;">
          <button class="btn" style="color: var(--primary-color); margin-right: 8px;"><i class="fas fa-edit"></i> 编辑</button>
          <button class="btn btn-primary"><i class="fas fa-plus-circle"></i> 添加</button>
        </div>
      </div>

      <!-- 饼图组件 -->
      <div class="card" style="height: 300px; display: flex; flex-direction: column;">
        <div style="padding: 16px; border-bottom: 1px solid var(--border-color);">
          <div style="font-size: 16px; font-weight: 500; display: flex; align-items: center;">
            <i class="fas fa-chart-pie" style="margin-right: 8px; color: var(--primary-color);"></i> 饼图组件
          </div>
          <div style="font-size: 12px; color: var(--text-tertiary); margin-top: 4px;">用于展示数据占比关系</div>
        </div>
        <div style="flex-grow: 1; display: flex; justify-content: center; align-items: center; padding: 16px;">
          <div style="width: 100%; height: 160px; background-color: var(--bg-secondary); border-radius: 4px; display: flex; justify-content: center; align-items: center;">
            <i class="fas fa-chart-pie" style="font-size: 48px; color: var(--text-tertiary);"></i>
          </div>
        </div>
        <div style="padding: 16px; border-top: 1px solid var(--border-color); display: flex; justify-content: flex-end;">
          <button class="btn" style="color: var(--primary-color); margin-right: 8px;"><i class="fas fa-edit"></i> 编辑</button>
          <button class="btn btn-primary"><i class="fas fa-plus-circle"></i> 添加</button>
        </div>
      </div>

      <!-- 表格组件 -->
      <div class="card" style="height: 300px; display: flex; flex-direction: column;">
        <div style="padding: 16px; border-bottom: 1px solid var(--border-color);">
          <div style="font-size: 16px; font-weight: 500; display: flex; align-items: center;">
            <i class="fas fa-table" style="margin-right: 8px; color: var(--primary-color);"></i> 表格组件
          </div>
          <div style="font-size: 12px; color: var(--text-tertiary); margin-top: 4px;">用于详细展示结构化数据</div>
        </div>
        <div style="flex-grow: 1; display: flex; justify-content: center; align-items: center; padding: 16px;">
          <div style="width: 100%; height: 160px; background-color: var(--bg-secondary); border-radius: 4px; display: flex; justify-content: center; align-items: center;">
            <i class="fas fa-table" style="font-size: 48px; color: var(--text-tertiary);"></i>
          </div>
        </div>
        <div style="padding: 16px; border-top: 1px solid var(--border-color); display: flex; justify-content: flex-end;">
          <button class="btn" style="color: var(--primary-color); margin-right: 8px;"><i class="fas fa-edit"></i> 编辑</button>
          <button class="btn btn-primary"><i class="fas fa-plus-circle"></i> 添加</button>
        </div>
      </div>
    </div>
  </div>

  <script src="js/common.js"></script>
</body>
</html>