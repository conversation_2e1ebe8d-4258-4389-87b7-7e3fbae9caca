<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>数智化运营平台 - 微服务部署管理</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <i class="fas fa-chart-line"></i> 数智化运营平台
      </div>
      <div style="display: flex; align-items: center;">
        <div class="dropdown" style="margin-right: 16px;">
          <button class="dropdown-toggle">
            <i class="fas fa-bell"></i>
            <span class="badge">1</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item">
              <div style="font-weight: 500;">部署成功通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">数据服务集群扩容成功</div>
            </div>
          </div>
        </div>
        <div class="dropdown">
          <button class="dropdown-toggle">
            <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px;">
            <span>管理员</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item"><i class="fas fa-user"></i> 个人中心</div>
            <div class="dropdown-item"><i class="fas fa-cog"></i> 系统设置</div>
            <div class="dropdown-item"><i class="fas fa-sign-out-alt"></i> 退出登录</div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- 侧边栏 -->
  <div class="sidebar">
    <div class="menu-item">
      <i class="fas fa-home menu-icon"></i>
      <span class="menu-text">首页</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-database menu-icon"></i>
      <span class="menu-text">数据融通</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-pie menu-icon"></i>
      <span class="menu-text">智能洞察分析</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-bar menu-icon"></i>
      <span class="menu-text">运营视图</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tachometer-alt menu-icon"></i>
      <span class="menu-text">统一运营门户</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tasks menu-icon"></i>
      <span class="menu-text">五级穿透调度</span>
    </div>
    <div class="menu-item active">
      <i class="fas fa-server menu-icon"></i>
      <span class="menu-text">微服务管理</span>
      <div style="margin-left: 24px; margin-top: 8px; font-size: 14px;">
        <div style="padding: 8px 0; color: var(--primary-color); cursor: pointer;">服务部署</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">集群管理</div>
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">CI/CD流水线</div>
      </div>
    </div>
    <div class="menu-item">
      <i class="fas fa-user-shield menu-icon"></i>
      <span class="menu-text">权限管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-cog menu-icon"></i>
      <span class="menu-text">系统设置</span>
    </div>
  </div>

  <!-- 主内容区 -->
  <div class="main-content">
    <div class="page-title">
      <i class="fas fa-cogs page-title-icon"></i>
      微服务部署管理
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit;">首页</a></div>
      <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit;">微服务管理</a></div>
      <div class="breadcrumb-item active">服务部署</div>
    </div>

    <!-- 微服务架构概览 -->
    <div class="card" style="margin-bottom: 20px;">
      <div class="card-header">
        <div class="card-title">微服务架构概览</div>
      </div>
      <div class="card-body">
        <div class="architecture-diagram">
          <div class="architecture-layer">
            <div class="layer-title">客户端层</div>
            <div class="service-box">Web客户端</div>
            <div class="service-box">移动端</div>
          </div>
          <div class="architecture-layer">
            <div class="layer-title">API网关层</div>
            <div class="service-box">负载均衡</div>
            <div class="service-box">认证授权</div>
            <div class="service-box">限流熔断</div>
          </div>
          <div class="architecture-layer">
            <div class="layer-title">微服务层</div>
            <div class="service-box">用户服务</div>
            <div class="service-box">数据服务</div>
            <div class="service-box">分析服务</div>
            <div class="service-box">任务调度服务</div>
            <div class="service-box">通知服务</div>
          </div>
          <div class="architecture-layer">
            <div class="layer-title">数据存储层</div>
            <div class="service-box">关系型数据库</div>
            <div class="service-box">NoSQL数据库</div>
            <div class="service-box">消息队列</div>
            <div class="service-box">缓存</div>
          </div>
        </div>
      </div>
    </div>

    <!-- CI/CD流水线 -->
    <div class="card" style="margin-bottom: 20px;">
      <div class="card-header">
        <div class="card-title">CI/CD流水线</div>
      </div>
      <div class="card-body">
        <div class="pipeline-diagram">
          <div class="pipeline-stage active">
            <div class="stage-icon"><i class="fas fa-code"></i></div>
            <div class="stage-title">代码提交</div>
            <div class="stage-status">已完成</div>
          </div>
          <div class="pipeline-connector active"></div>
          <div class="pipeline-stage active">
            <div class="stage-icon"><i class="fas fa-tools"></i></div>
            <div class="stage-title">构建</div>
            <div class="stage-status">已完成</div>
          </div>
          <div class="pipeline-connector active"></div>
          <div class="pipeline-stage active">
            <div class="stage-icon"><i class="fas fa-vial"></i></div>
            <div class="stage-title">测试</div>
            <div class="stage-status">已完成</div>
          </div>
          <div class="pipeline-connector active"></div>
          <div class="pipeline-stage active">
            <div class="stage-icon"><i class="fas fa-archive"></i></div>
            <div class="stage-title">打包</div>
            <div class="stage-status">已完成</div>
          </div>
          <div class="pipeline-connector active"></div>
          <div class="pipeline-stage active">
            <div class="stage-icon"><i class="fas fa-ship"></i></div>
            <div class="stage-title">部署</div>
            <div class="stage-status">进行中</div>
          </div>
          <div class="pipeline-connector"></div>
          <div class="pipeline-stage">
            <div class="stage-icon"><i class="fas fa-check-circle"></i></div>
            <div class="stage-title">验证</div>
            <div class="stage-status">未开始</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 容器部署和集群状态 -->
    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
      <!-- 容器部署流程 -->
      <div class="card">
        <div class="card-header">
          <div class="card-title">容器部署流程</div>
        </div>
        <div class="card-body">
          <div class="deployment-steps">
            <div class="step active">
              <div class="step-number">1</div>
              <div class="step-content">
                <div class="step-title">准备Docker镜像</div>
                <div class="step-description">从代码构建Docker镜像并推送到镜像仓库</div>
              </div>
            </div>
            <div class="step active">
              <div class="step-number">2</div>
              <div class="step-content">
                <div class="step-title">配置Kubernetes部署文件</div>
                <div class="step-description">定义Deployment、Service和Ingress资源</div>
              </div>
            </div>
            <div class="step active">
              <div class="step-number">3</div>
              <div class="step-content">
                <div class="step-title">应用部署配置</div>
                <div class="step-description">使用kubectl apply部署应用到Kubernetes集群</div>
              </div>
            </div>
            <div class="step active">
              <div class="step-number">4</div>
              <div class="step-content">
                <div class="step-title">监控部署状态</div>
                <div class="step-description">查看Pod状态和日志，确保部署成功</div>
              </div>
            </div>
            <div class="step">
              <div class="step-number">5</div>
              <div class="step-content">
                <div class="step-title">验证服务</div>
                <div class="step-description">通过服务URL访问应用，验证功能正常</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 集群状态 -->
      <div class="card">
        <div class="card-header">
          <div class="card-title">集群状态</div>
        </div>
        <div class="card-body">
          <div class="cluster-status">
            <div class="status-item">
              <div class="status-label">节点数量</div>
              <div class="status-value">6</div>
            </div>
            <div class="status-item">
              <div class="status-label">运行中Pod</div>
              <div class="status-value">24</div>
            </div>
            <div class="status-item">
              <div class="status-label">CPU使用率</div>
              <div class="status-value">62%</div>
              <div class="progress-bar"><div class="progress" style="width: 62%; background-color: var(--warning-color);"></div></div>
            </div>
            <div class="status-item">
              <div class="status-label">内存使用率</div>
              <div class="status-value">58%</div>
              <div class="progress-bar"><div class="progress" style="width: 58%; background-color: var(--primary-color);"></div></div>
            </div>
            <div class="status-item">
              <div class="status-label">存储使用率</div>
              <div class="status-value">45%</div>
              <div class="progress-bar"><div class="progress" style="width: 45%; background-color: var(--success-color);"></div></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 扩容操作和部署历史 -->
    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
      <!-- 扩容操作 -->
      <div class="card">
        <div class="card-header">
          <div class="card-title">一键式扩容</div>
        </div>
        <div class="card-body">
          <div class="scaling-form">
            <div class="form-group">
              <label for="serviceSelect">选择服务</label>
              <select id="serviceSelect" name="serviceSelect" required>
                <option value="">请选择服务</option>
                <option value="user-service">用户服务</option>
                <option value="data-service">数据服务</option>
                <option value="analysis-service">分析服务</option>
                <option value="task-service">任务调度服务</option>
                <option value="notification-service">通知服务</option>
              </select>
            </div>
            <div class="form-group">
              <label for="replicaCount">副本数量</label>
              <input type="number" id="replicaCount" name="replicaCount" min="1" max="20" value="3" required>
            </div>
            <div class="form-group">
              <label for="scalingStrategy">扩容策略</label>
              <select id="scalingStrategy" name="scalingStrategy" required>
                <option value="manual">手动扩容</option>
                <option value="auto">自动扩容</option>
              </select>
            </div>
            <div class="form-group" id="autoScalingConfig" style="display: none;">
              <label for="cpuThreshold">CPU阈值 (%)</label>
              <input type="number" id="cpuThreshold" name="cpuThreshold" min="1" max="99" value="70">
            </div>
            <button class="btn btn-primary" style="width: 100%;" onclick="scaleService()">执行扩容</button>
          </div>
        </div>
      </div>

      <!-- 部署历史 -->
      <div class="card">
        <div class="card-header">
          <div class="card-title">部署历史</div>
        </div>
        <div class="card-body">
          <div class="deployment-history">
            <div class="history-item">
              <div class="history-time">2023-07-16 14:30</div>
              <div class="history-service">数据服务</div>
              <div class="history-action"><span class="tag tag-success">扩容</span></div>
              <div class="history-details">副本数从3增加到5</div>
            </div>
            <div class="history-item">
              <div class="history-time">2023-07-15 09:15</div>
              <div class="history-service">分析服务</div>
              <div class="history-action"><span class="tag tag-primary">部署</span></div>
              <div class="history-details">版本v1.2.0</div>
            </div>
            <div class="history-item">
              <div class="history-time">2023-07-14 16:45</div>
              <div class="history-service">用户服务</div>
              <div class="history-action"><span class="tag tag-warning">回滚</span></div>
              <div class="history-details">回滚到v1.1.0</div>
            </div>
            <div class="history-item">
              <div class="history-time">2023-07-14 10:20</div>
              <div class="history-service">用户服务</div>
              <div class="history-action"><span class="tag tag-primary">部署</span></div>
              <div class="history-details">版本v1.1.1</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="js/common.js"></script>
  <script>
    // 扩容策略选择切换
    document.getElementById('scalingStrategy').addEventListener('change', function() {
      if (this.value === 'auto') {
        document.getElementById('autoScalingConfig').style.display = 'block';
      } else {
        document.getElementById('autoScalingConfig').style.display = 'none';
      }
    });

    // 执行扩容
    function scaleService() {
      const service = document.getElementById('serviceSelect').value;
      const replicas = document.getElementById('replicaCount').value;
      const strategy = document.getElementById('scalingStrategy').value;

      if (!service) {
        alert('请选择服务');
        return;
      }

      // 模拟扩容操作
      alert(`正在对${service}执行扩容，副本数调整为${replicas}...`);

      // 模拟扩容成功
      setTimeout(() => {
        alert(`扩容成功！${service}副本数已调整为${replicas}`);
      }, 2000);
    }

    // 添加微服务架构图样式
    document.addEventListener('DOMContentLoaded', function() {
      // 架构图样式
      const architectureLayers = document.querySelectorAll('.architecture-layer');
      architectureLayers.forEach((layer, index) => {
        layer.style.display = 'flex';
        layer.style.justifyContent = 'center';
        layer.style.alignItems = 'center';
        layer.style.marginBottom = '20px';
        layer.style.padding = '10px';
        layer.style.borderRadius = '8px';
        layer.style.backgroundColor = `rgba(var(--primary-rgb), ${0.1 + index * 0.1})`;
      });

      const layerTitles = document.querySelectorAll('.layer-title');
      layerTitles.forEach(title => {
        title.style.width = '100px';
        title.style.fontWeight = 'bold';
        title.style.textAlign = 'right';
        title.style.marginRight = '15px';
      });

      const serviceBoxes = document.querySelectorAll('.service-box');
      serviceBoxes.forEach(box => {
        box.style.margin = '0 10px';
        box.style.padding = '8px 15px';
        box.style.borderRadius = '4px';
        box.style.backgroundColor = 'var(--primary-color)';
        box.style.color = 'white';
        box.style.fontSize = '14px';
      });

      // CI/CD流水线样式
      const pipelineStages = document.querySelectorAll('.pipeline-stage');
      pipelineStages.forEach(stage => {
        stage.style.display = 'flex';
        stage.style.flexDirection = 'column';
        stage.style.alignItems = 'center';
        stage.style.margin = '0 10px';
      });

      const stageIcons = document.querySelectorAll('.stage-icon');
      stageIcons.forEach(icon => {
        icon.style.width = '40px';
        icon.style.height = '40px';
        icon.style.borderRadius = '50%';
        icon.style.display = 'flex';
        icon.style.justifyContent = 'center';
        icon.style.alignItems = 'center';
        icon.style.backgroundColor = 'var(--primary-color)';
        icon.style.color = 'white';
        icon.style.marginBottom = '8px';
      });

      const pipelineConnectors = document.querySelectorAll('.pipeline-connector');
      pipelineConnectors.forEach(connector => {
        connector.style.width = '40px';
        connector.style.height = '2px';
        connector.style.backgroundColor = '#ddd';
      });

      document.querySelector('.pipeline-diagram').style.display = 'flex';
      document.querySelector('.pipeline-diagram').style.alignItems = 'center';

      // 激活的流水线元素样式
      const activeStages = document.querySelectorAll('.pipeline-stage.active');
      activeStages.forEach(stage => {
        stage.querySelector('.stage-icon').style.backgroundColor = 'var(--success-color)';
      });

      const activeConnectors = document.querySelectorAll('.pipeline-connector.active');
      activeConnectors.forEach(connector => {
        connector.style.backgroundColor = 'var(--success-color)';
      });
    });
  </script>
</body>
</html>