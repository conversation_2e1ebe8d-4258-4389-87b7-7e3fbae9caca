// 侧边栏菜单切换
function initSidebar() {
  const menuItems = document.querySelectorAll('.menu-item');
  menuItems.forEach(item => {
    item.addEventListener('click', () => {
      menuItems.forEach(mi => mi.classList.remove('active'));
      item.classList.add('active');
    });
  });
}

// 模态框控制
function initModals() {
  const modalTriggers = document.querySelectorAll('[data-modal-target]');
  const modals = document.querySelectorAll('.modal');
  const modalCloses = document.querySelectorAll('.modal-close');
  const modalBackdrops = document.querySelectorAll('.modal');

  modalTriggers.forEach(trigger => {
    trigger.addEventListener('click', () => {
      const modalId = trigger.getAttribute('data-modal-target');
      const modal = document.getElementById(modalId);
      modal.classList.add('show');
    });
  });

  modalCloses.forEach(close => {
    close.addEventListener('click', () => {
      const modal = close.closest('.modal');
      modal.classList.remove('show');
    });
  });

  modalBackdrops.forEach(backdrop => {
    backdrop.addEventListener('click', (e) => {
      if (e.target === backdrop) {
        backdrop.classList.remove('show');
      }
    });
  });
}

// 标签页切换
function initTabs() {
  const tabItems = document.querySelectorAll('.tab-item');
  tabItems.forEach(item => {
    item.addEventListener('click', () => {
      const tabId = item.getAttribute('data-tab-target');
      const tabContent = document.getElementById(tabId);

      // 移除所有活动状态
      document.querySelectorAll('.tab-item').forEach(ti => ti.classList.remove('active'));
      document.querySelectorAll('.tab-content').forEach(tc => tc.classList.remove('active'));

      // 添加当前活动状态
      item.classList.add('active');
      tabContent.classList.add('active');
    });
  });
}

// 下拉菜单
function initDropdowns() {
  const dropdownToggles = document.querySelectorAll('.dropdown-toggle');
  dropdownToggles.forEach(toggle => {
    toggle.addEventListener('click', (e) => {
      e.stopPropagation();
      const dropdown = toggle.closest('.dropdown');
      dropdown.classList.toggle('show');
    });
  });

  // 点击其他地方关闭下拉菜单
  document.addEventListener('click', () => {
    document.querySelectorAll('.dropdown.show').forEach(dropdown => {
      dropdown.classList.remove('show');
    });
  });
}

// 步骤条控制
function initSteps() {
  const stepButtons = document.querySelectorAll('[data-step]');
  stepButtons.forEach(button => {
    button.addEventListener('click', () => {
      const step = parseInt(button.getAttribute('data-step'));
      const steps = document.querySelectorAll('.step');

      steps.forEach((s, index) => {
        if (index < step) {
          s.classList.add('active');
        } else {
          s.classList.remove('active');
        }
      });
    });
  });
}

// 表单验证
function validateForm(formId) {
  const form = document.getElementById(formId);
  if (!form) return true;

  let isValid = true;
  const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');

  inputs.forEach(input => {
    if (!input.value.trim()) {
      isValid = false;
      input.classList.add('error');
      setTimeout(() => {
        input.classList.remove('error');
      }, 3000);
    }
  });

  return isValid;
}

// 页面导航函数
function navigateToPage(pageUrl) {
  window.location.href = pageUrl;
}

// 面包屑导航刷新效果
function initBreadcrumbRefresh() {
  const breadcrumbItems = document.querySelectorAll('.breadcrumb-item');
  breadcrumbItems.forEach(item => {
    item.addEventListener('click', (e) => {
      // 如果是链接，阻止默认行为
      const link = item.querySelector('a');
      if (link) {
        e.preventDefault();
        // 添加旋转动画类
        item.classList.add('refreshing');
        // 1秒后移除动画类并跳转
        setTimeout(() => {
          item.classList.remove('refreshing');
          window.location.href = link.getAttribute('href');
        }, 1000);
      } else if (item.classList.contains('active')) {
        // 为当前激活的项添加刷新动画
        item.classList.add('refreshing');
        // 1秒后移除动画类并刷新页面
        setTimeout(() => {
          item.classList.remove('refreshing');
          location.reload();
        }, 1000);
      }
    });
  });
}

// 初始化所有组件
function initComponents() {
  initSidebar();
  initModals();
  initTabs();
  initDropdowns();
  initSteps();
  initBreadcrumbRefresh();

  // 添加表单提交事件
  const forms = document.querySelectorAll('form');
  forms.forEach(form => {
    form.addEventListener('submit', (e) => {
      if (!validateForm(form.id)) {
        e.preventDefault();
        alert('请填写所有必填字段');
      }
    });
  });
}

// 页面加载完成后初始化组件
document.addEventListener('DOMContentLoaded', initComponents);