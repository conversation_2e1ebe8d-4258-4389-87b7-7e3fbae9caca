<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>容器部署 - 数智化运营平台</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <i class="fas fa-chart-line"></i> 数智化运营平台
      </div>
      <div style="display: flex; align-items: center;">
        <div class="dropdown" style="margin-right: 16px;">
          <button class="dropdown-toggle">
            <i class="fas fa-bell"></i>
            <span class="badge">3</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item">
              <div style="font-weight: 500;">新任务通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">您有3个新任务需要处理</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">数据采集完成</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">昨日数据采集已完成</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">系统更新</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">平台将于今晚23:00进行维护</div>
            </div>
          </div>
        </div>
        <div class="dropdown">
          <button class="dropdown-toggle">
            <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px;">
            <span>管理员</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item"><i class="fas fa-user"></i> 个人中心</div>
            <div class="dropdown-item"><i class="fas fa-cog"></i> 系统设置</div>
            <div class="dropdown-item"><i class="fas fa-sign-out-alt"></i> 退出登录</div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- 侧边栏 -->
  <div class="sidebar">
    <div class="menu-item" onclick="navigateToPage('index.html')">
      <i class="fas fa-home menu-icon"></i>
      <span class="menu-text">首页</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-database menu-icon"></i>
      <span class="menu-text">数据融通</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-pie menu-icon"></i>
      <span class="menu-text">智能洞察分析</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-bar menu-icon"></i>
      <span class="menu-text">运营视图</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tachometer-alt menu-icon"></i>
      <span class="menu-text">统一运营门户</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tasks menu-icon"></i>
      <span class="menu-text">五级穿透调度</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-server menu-icon"></i>
      <span class="menu-text">微服务管理</span>
    </div>
    
    <!-- DevOps 模块 -->
    <div class="menu-section">
      <div class="menu-section-title">DevOps 平台</div>
    </div>
    <div class="menu-item" onclick="navigateToPage('devops_dashboard.html')">
      <i class="fas fa-tachometer-alt menu-icon"></i>
      <span class="menu-text">DevOps 总览</span>
    </div>
    <div class="menu-item" onclick="navigateToPage('pipeline_management.html')">
      <i class="fas fa-code-branch menu-icon"></i>
      <span class="menu-text">CI/CD 流水线</span>
    </div>
    <div class="menu-item active" onclick="navigateToPage('deployment_management.html')">
      <i class="fas fa-cube menu-icon"></i>
      <span class="menu-text">容器部署</span>
    </div>
    <div class="menu-item" onclick="navigateToPage('monitoring_center.html')">
      <i class="fas fa-chart-line menu-icon"></i>
      <span class="menu-text">监控中心</span>
    </div>
    <div class="menu-item" onclick="navigateToPage('service_topology.html')">
      <i class="fas fa-project-diagram menu-icon"></i>
      <span class="menu-text">服务拓扑</span>
    </div>
    
    <div class="menu-item">
      <i class="fas fa-user-shield menu-icon"></i>
      <span class="menu-text">权限管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-cog menu-icon"></i>
      <span class="menu-text">系统设置</span>
    </div>
  </div>

  <!-- 主内容区 -->
  <div class="main-content">
    <div class="page-title">
      <i class="fas fa-cube page-title-icon"></i>
      容器部署管理
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit;">首页</a></div>
      <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit;">DevOps 平台</a></div>
      <div class="breadcrumb-item active">容器部署</div>
    </div>

    <!-- 页面标题和操作 -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px;">
      <div>
        <h1 style="font-size: 24px; font-weight: bold; margin: 0; color: var(--text-primary);">容器部署管理</h1>
        <p style="color: var(--text-secondary); margin: 4px 0 0 0;">微服务容器化部署与扩缩容管理</p>
      </div>
      <button class="btn btn-primary" onclick="createNewDeployment()">
        <i class="fas fa-cube"></i> 部署新应用
      </button>
    </div>

    <!-- 环境选择 -->
    <div class="card" style="padding: 16px; margin-bottom: 24px;">
      <div style="display: flex; align-items: center; gap: 16px;">
        <span style="font-size: 14px; font-weight: 500; color: var(--text-primary);">环境:</span>
        <div id="environmentTabs" style="display: flex; gap: 8px;">
          <!-- 环境标签将通过JavaScript动态生成 -->
        </div>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="row">
      <div class="col col-3">
        <div class="stat-card">
          <div class="stat-icon" style="background-color: rgba(24, 144, 255, 0.1); color: var(--primary-color);">
            <i class="fas fa-cube"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value" id="totalApps">12</div>
            <div class="stat-label">应用总数</div>
          </div>
        </div>
      </div>
      <div class="col col-3">
        <div class="stat-card">
          <div class="stat-icon" style="background-color: rgba(82, 196, 26, 0.1); color: var(--success-color);">
            <i class="fas fa-chart-line"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value" id="healthyApps">10</div>
            <div class="stat-label">健康应用</div>
          </div>
        </div>
      </div>
      <div class="col col-3">
        <div class="stat-card">
          <div class="stat-icon" style="background-color: rgba(255, 77, 79, 0.1); color: var(--danger-color);">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value" id="unhealthyApps">2</div>
            <div class="stat-label">异常应用</div>
          </div>
        </div>
      </div>
      <div class="col col-3">
        <div class="stat-card">
          <div class="stat-icon" style="background-color: rgba(24, 144, 255, 0.1); color: var(--primary-color);">
            <i class="fas fa-microchip"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value" id="runningInstances">24/30</div>
            <div class="stat-label">运行实例</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 应用列表 -->
    <div style="margin-bottom: 24px;">
      <h3 style="font-size: 18px; font-weight: 600; color: var(--text-primary); margin-bottom: 16px;">
        <span id="currentEnvironmentName">生产环境</span> - 应用列表
      </h3>
      <div id="applicationsList">
        <!-- 应用列表将通过JavaScript动态生成 -->
      </div>
    </div>
  </div>

  <!-- 扩缩容模态框 -->
  <div class="modal" id="scalingModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-expand-arrows-alt"></i> 应用扩缩容</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <div id="scalingAppInfo" style="margin-bottom: 20px;">
          <!-- 应用信息将通过JavaScript动态生成 -->
        </div>
        <div class="form-group">
          <label for="instanceCount">实例数量</label>
          <div style="display: flex; align-items: center; gap: 12px;">
            <button type="button" onclick="adjustInstanceCount(-1)" style="width: 32px; height: 32px; border: 1px solid var(--border-color); background: white; border-radius: 4px; cursor: pointer;">-</button>
            <input type="number" id="instanceCount" min="1" max="20" value="3" style="width: 80px; text-align: center;">
            <button type="button" onclick="adjustInstanceCount(1)" style="width: 32px; height: 32px; border: 1px solid var(--border-color); background: white; border-radius: 4px; cursor: pointer;">+</button>
          </div>
          <div style="font-size: 12px; color: var(--text-tertiary); margin-top: 4px;">
            建议范围: 1-20 个实例
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn" style="border: 1px solid var(--border-color);" onclick="closeScalingModal()">取消</button>
        <button class="btn btn-primary" onclick="confirmScaling()">确认扩缩容</button>
      </div>
    </div>
  </div>

  <script src="js/common.js"></script>
  <script>
    // 模拟数据
    const environments = [
      { id: 'prod', name: '生产环境' },
      { id: 'staging', name: '预发布环境' },
      { id: 'dev', name: '开发环境' }
    ];

    let selectedEnvironment = environments[0];
    let scalingApp = null;

    const applications = {
      prod: [
        {
          id: 'user-service',
          name: 'user-service',
          description: '用户服务',
          status: 'healthy',
          instances: { current: 3, desired: 3 },
          version: 'v1.2.3',
          image: 'registry.company.com/user-service:v1.2.3',
          cpu: { usage: 45, limit: 100 },
          memory: { usage: 512, limit: 1024 },
          lastDeployed: '2024-01-15T10:30:00Z',
          health: { ready: 3, total: 3 }
        },
        {
          id: 'order-service',
          name: 'order-service',
          description: '订单服务',
          status: 'healthy',
          instances: { current: 5, desired: 5 },
          version: 'v2.1.0',
          image: 'registry.company.com/order-service:v2.1.0',
          cpu: { usage: 78, limit: 200 },
          memory: { usage: 1024, limit: 2048 },
          lastDeployed: '2024-01-15T09:45:00Z',
          health: { ready: 5, total: 5 }
        },
        {
          id: 'payment-service',
          name: 'payment-service',
          description: '支付服务',
          status: 'unhealthy',
          instances: { current: 2, desired: 3 },
          version: 'v1.5.2',
          image: 'registry.company.com/payment-service:v1.5.2',
          cpu: { usage: 95, limit: 150 },
          memory: { usage: 1536, limit: 1536 },
          lastDeployed: '2024-01-15T08:20:00Z',
          health: { ready: 2, total: 3 }
        },
        {
          id: 'notification-service',
          name: 'notification-service',
          description: '通知服务',
          status: 'healthy',
          instances: { current: 2, desired: 2 },
          version: 'v1.0.8',
          image: 'registry.company.com/notification-service:v1.0.8',
          cpu: { usage: 25, limit: 50 },
          memory: { usage: 256, limit: 512 },
          lastDeployed: '2024-01-14T16:15:00Z',
          health: { ready: 2, total: 2 }
        }
      ],
      staging: [
        {
          id: 'user-service-staging',
          name: 'user-service',
          description: '用户服务（预发布）',
          status: 'healthy',
          instances: { current: 2, desired: 2 },
          version: 'v1.2.4-rc1',
          image: 'registry.company.com/user-service:v1.2.4-rc1',
          cpu: { usage: 35, limit: 100 },
          memory: { usage: 384, limit: 512 },
          lastDeployed: '2024-01-15T14:20:00Z',
          health: { ready: 2, total: 2 }
        }
      ],
      dev: [
        {
          id: 'user-service-dev',
          name: 'user-service',
          description: '用户服务（开发）',
          status: 'healthy',
          instances: { current: 1, desired: 1 },
          version: 'v1.3.0-dev',
          image: 'registry.company.com/user-service:v1.3.0-dev',
          cpu: { usage: 20, limit: 50 },
          memory: { usage: 256, limit: 512 },
          lastDeployed: '2024-01-15T15:45:00Z',
          health: { ready: 1, total: 1 }
        }
      ]
    };

    // 获取状态颜色和图标
    function getStatusColor(status) {
      switch (status) {
        case 'healthy': return 'var(--success-color)';
        case 'unhealthy': return 'var(--danger-color)';
        case 'warning': return 'var(--warning-color)';
        default: return 'var(--text-tertiary)';
      }
    }

    function getStatusIcon(status) {
      switch (status) {
        case 'healthy': return '<i class="fas fa-check-circle" style="color: var(--success-color);"></i>';
        case 'unhealthy': return '<i class="fas fa-exclamation-circle" style="color: var(--danger-color);"></i>';
        case 'warning': return '<i class="fas fa-exclamation-triangle" style="color: var(--warning-color);"></i>';
        default: return '<i class="fas fa-question-circle" style="color: var(--text-tertiary);"></i>';
      }
    }

    function getStatusText(status) {
      switch (status) {
        case 'healthy': return '健康';
        case 'unhealthy': return '异常';
        case 'warning': return '警告';
        default: return '未知';
      }
    }

    // 渲染环境标签
    function renderEnvironmentTabs() {
      const container = document.getElementById('environmentTabs');
      container.innerHTML = environments.map(env => `
        <button
          class="env-tab ${selectedEnvironment.id === env.id ? 'active' : ''}"
          onclick="selectEnvironment('${env.id}')"
          style="padding: 8px 16px; border-radius: 4px; font-size: 14px; font-weight: 500; transition: all 0.2s; border: 1px solid var(--border-color); background: ${selectedEnvironment.id === env.id ? 'var(--primary-color)' : 'var(--bg-color)'}; color: ${selectedEnvironment.id === env.id ? 'white' : 'var(--text-primary)'};"
        >
          ${env.name}
        </button>
      `).join('');
    }

    // 渲染应用列表
    function renderApplicationsList() {
      const container = document.getElementById('applicationsList');
      const apps = applications[selectedEnvironment.id] || [];

      container.innerHTML = apps.map(app => `
        <div class="card" style="margin-bottom: 16px; padding: 20px;">
          <div style="display: flex; justify-content: space-between; align-items: flex-start;">
            <div style="flex: 1;">
              <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 12px;">
                ${getStatusIcon(app.status)}
                <h4 style="font-size: 18px; font-weight: 600; color: var(--text-primary); margin: 0;">
                  ${app.name}
                </h4>
                <span class="tag" style="background-color: ${getStatusColor(app.status)}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                  ${getStatusText(app.status)}
                </span>
              </div>
              <p style="color: var(--text-secondary); margin: 0 0 16px 0; font-size: 14px;">
                ${app.description}
              </p>

              <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
                <div>
                  <div style="font-size: 12px; color: var(--text-tertiary); margin-bottom: 4px;">版本</div>
                  <div style="font-size: 14px; color: var(--text-primary); font-weight: 500;">${app.version}</div>
                </div>
                <div>
                  <div style="font-size: 12px; color: var(--text-tertiary); margin-bottom: 4px;">实例</div>
                  <div style="font-size: 14px; color: var(--text-primary); font-weight: 500;">
                    ${app.health.ready}/${app.health.total} 就绪
                  </div>
                </div>
                <div>
                  <div style="font-size: 12px; color: var(--text-tertiary); margin-bottom: 4px;">CPU 使用率</div>
                  <div style="display: flex; align-items: center; gap: 8px;">
                    <div style="flex: 1; height: 6px; background-color: var(--bg-color); border-radius: 3px; overflow: hidden;">
                      <div style="height: 100%; background-color: ${app.cpu.usage > 80 ? 'var(--danger-color)' : app.cpu.usage > 60 ? 'var(--warning-color)' : 'var(--success-color)'}; width: ${(app.cpu.usage / app.cpu.limit) * 100}%;"></div>
                    </div>
                    <span style="font-size: 12px; color: var(--text-secondary);">${app.cpu.usage}%</span>
                  </div>
                </div>
                <div>
                  <div style="font-size: 12px; color: var(--text-tertiary); margin-bottom: 4px;">内存使用</div>
                  <div style="display: flex; align-items: center; gap: 8px;">
                    <div style="flex: 1; height: 6px; background-color: var(--bg-color); border-radius: 3px; overflow: hidden;">
                      <div style="height: 100%; background-color: ${(app.memory.usage / app.memory.limit) > 0.8 ? 'var(--danger-color)' : (app.memory.usage / app.memory.limit) > 0.6 ? 'var(--warning-color)' : 'var(--success-color)'}; width: ${(app.memory.usage / app.memory.limit) * 100}%;"></div>
                    </div>
                    <span style="font-size: 12px; color: var(--text-secondary);">${app.memory.usage}MB</span>
                  </div>
                </div>
              </div>
            </div>

            <div style="display: flex; flex-direction: column; gap: 8px; margin-left: 20px;">
              <button class="btn btn-primary" onclick="scaleApplication('${app.id}')" style="font-size: 12px; padding: 6px 12px;">
                <i class="fas fa-expand-arrows-alt"></i> 扩缩容
              </button>
              <button class="btn" style="font-size: 12px; padding: 6px 12px; border: 1px solid var(--border-color);" onclick="restartApplication('${app.id}')">
                <i class="fas fa-redo"></i> 重启
              </button>
              <button class="btn" style="font-size: 12px; padding: 6px 12px; border: 1px solid var(--danger-color); color: var(--danger-color);" onclick="stopApplication('${app.id}')">
                <i class="fas fa-stop"></i> 停止
              </button>
            </div>
          </div>
        </div>
      `).join('');
    }

    // 更新统计数据
    function updateStats() {
      const apps = applications[selectedEnvironment.id] || [];
      const totalApps = apps.length;
      const healthyApps = apps.filter(app => app.status === 'healthy').length;
      const unhealthyApps = apps.filter(app => app.status === 'unhealthy').length;
      const totalInstances = apps.reduce((sum, app) => sum + app.health.total, 0);
      const runningInstances = apps.reduce((sum, app) => sum + app.health.ready, 0);

      document.getElementById('totalApps').textContent = totalApps;
      document.getElementById('healthyApps').textContent = healthyApps;
      document.getElementById('unhealthyApps').textContent = unhealthyApps;
      document.getElementById('runningInstances').textContent = `${runningInstances}/${totalInstances}`;
    }

    // 选择环境
    function selectEnvironment(envId) {
      selectedEnvironment = environments.find(env => env.id === envId);
      document.getElementById('currentEnvironmentName').textContent = selectedEnvironment.name;
      renderEnvironmentTabs();
      renderApplicationsList();
      updateStats();
    }

    // 扩缩容相关功能
    function scaleApplication(appId) {
      const apps = applications[selectedEnvironment.id] || [];
      scalingApp = apps.find(app => app.id === appId);
      if (scalingApp) {
        document.getElementById('scalingAppInfo').innerHTML = `
          <div style="padding: 16px; background-color: var(--bg-color); border-radius: 4px; border-left: 3px solid var(--primary-color);">
            <h4 style="margin: 0 0 8px 0; color: var(--text-primary);">${scalingApp.name}</h4>
            <p style="margin: 0; color: var(--text-secondary); font-size: 14px;">${scalingApp.description}</p>
            <div style="margin-top: 8px; font-size: 12px; color: var(--text-tertiary);">
              当前实例数: ${scalingApp.instances.current} | 期望实例数: ${scalingApp.instances.desired}
            </div>
          </div>
        `;
        document.getElementById('instanceCount').value = scalingApp.instances.desired;
        document.getElementById('scalingModal').classList.add('show');
      }
    }

    function adjustInstanceCount(delta) {
      const input = document.getElementById('instanceCount');
      const currentValue = parseInt(input.value);
      const newValue = Math.max(1, Math.min(20, currentValue + delta));
      input.value = newValue;
    }

    function closeScalingModal() {
      document.getElementById('scalingModal').classList.remove('show');
      scalingApp = null;
    }

    function confirmScaling() {
      if (scalingApp) {
        const newInstanceCount = parseInt(document.getElementById('instanceCount').value);
        scalingApp.instances.desired = newInstanceCount;
        scalingApp.instances.current = newInstanceCount; // 模拟立即生效
        scalingApp.health.total = newInstanceCount;
        scalingApp.health.ready = newInstanceCount;

        alert(`${scalingApp.name} 已成功扩缩容至 ${newInstanceCount} 个实例`);
        closeScalingModal();
        renderApplicationsList();
        updateStats();
      }
    }

    // 其他应用操作
    function restartApplication(appId) {
      alert('重启应用: ' + appId);
    }

    function stopApplication(appId) {
      if (confirm('确定要停止这个应用吗？')) {
        alert('停止应用: ' + appId);
      }
    }

    function createNewDeployment() {
      alert('创建新部署功能');
    }

    // 初始化页面
    document.addEventListener('DOMContentLoaded', function() {
      renderEnvironmentTabs();
      renderApplicationsList();
      updateStats();
    });
  </script>

  <style>
    .env-tab:hover {
      background-color: var(--hover-color) !important;
      color: var(--text-primary) !important;
    }

    .env-tab.active:hover {
      background-color: var(--secondary-color) !important;
      color: white !important;
    }
  </style>
</body>
</html>
