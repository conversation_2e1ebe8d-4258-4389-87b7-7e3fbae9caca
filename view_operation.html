<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视图操作能力 - 运营视图</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            color: #333;
        }

        /* 内容区样式 */
        .content {
            margin-left: 240px;
            padding: 20px;
            transition: margin-left 0.3s;
        }

        @media (max-width: 768px) {
            .content {
                margin-left: 0;
            }
        }

        /* 操作栏样式 */
        .operation-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }

        .operation-bar .title {
            font-size: 18px;
            font-weight: 600;
        }

        .operation-bar .actions {
            display: flex;
            gap: 10px;
        }

        /* 操作卡片样式 */
        .operation-cards {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .operation-card {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
        }

        .operation-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        }

        .card-icon {
            width: 48px;
            height: 48px;
            background-color: #e6f7ff;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            font-size: 24px;
            color: #1890ff;
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .card-desc {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
        }

        /* 操作日志样式 */
        .operation-logs {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .logs-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .logs-header h3 {
            font-size: 16px;
            font-weight: 600;
        }

        .logs-filter {
            display: flex;
            gap: 10px;
        }

        .log-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .log-table th,
        .log-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .log-table th {
            background-color: #f5f7fa;
            font-weight: 600;
        }

        .log-table tr:hover {
            background-color: #fafafa;
        }

        .log-type {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .type-view {
            background-color: #e6f7ff;
            color: #1890ff;
        }

        .type-edit {
            background-color: #fff7e6;
            color: #faad14;
        }

        .type-delete {
            background-color: #fff1f0;
            color: #f5222d;
        }

        .type-share {
            background-color: #e6fffb;
            color: #36cfc9;
        }

        /* 按钮样式 */
        .btn {
            display: inline-block;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.3s;
        }

        .btn-primary {
            background-color: #1890ff;
            color: white;
        }

        .btn-primary:hover {
            background-color: #40a9ff;
        }

        .btn-secondary {
            background-color: #f5f5f5;
            color: #333;
        }

        .btn-secondary:hover {
            background-color: #e8e8e8;
        }

        .btn-success {
            background-color: #52c41a;
            color: white;
        }

        .btn-success:hover {
            background-color: #73d13d;
        }

        .btn-danger {
            background-color: #f5222d;
            color: white;
        }

        .btn-danger:hover {
            background-color: #ff4d4f;
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="logo-container">
            <img src="img/logo.png" alt="Logo" class="logo">
            <span class="system-name">政企移动项目管理系统</span>
        </div>
        <div class="user-info">
            <span class="user-name">管理员</span>
            <img src="img/user-avatar.png" alt="User Avatar" class="user-avatar">
        </div>
    </header>

    <!-- 侧边栏 -->
    <aside class="sidebar">
        <div class="menu">
            <div class="menu-item">
                <i class="fas fa-home menu-icon"></i>
                <span class="menu-text">首页</span>
            </div>
            <div class="menu-item">
                <i class="fas fa-database menu-icon"></i>
                <span class="menu-text">数据融通</span>
            </div>
            <div class="menu-item">
                <i class="fas fa-brain menu-icon"></i>
                <span class="menu-text">智能洞察分析</span>
            </div>
            <div class="menu-item active">
                <i class="fas fa-chart-bar menu-icon"></i>
                <span class="menu-text">运营视图</span>
                <div style="margin-left: 24px; margin-top: 8px; font-size: 14px;">
                    <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">预置大屏模板</div>
                    <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">预制组件</div>
                    <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">模板管理</div>
                    <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">画布管理</div>
                    <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">自定义报表</div>
                    <div style="padding: 8px 0; color: var(--primary-color); cursor: pointer;">视图操作能力</div>
                </div>
            </div>
            <div class="menu-item">
                <i class="fas fa-desktop menu-icon"></i>
                <span class="menu-text">统一运营门户</span>
            </div>
            <div class="menu-item">
                <i class="fas fa-sitemap menu-icon"></i>
                <span class="menu-text">五级穿透调度</span>
            </div>
            <div class="menu-item">
                <i class="fas fa-microchip menu-icon"></i>
                <span class="menu-text">微服务管理</span>
            </div>
            <div class="menu-item">
                <i class="fas fa-key menu-icon"></i>
                <span class="menu-text">权限管理</span>
            </div>
            <div class="menu-item">
                <i class="fas fa-cog menu-icon"></i>
                <span class="menu-text">系统设置</span>
            </div>
        </div>
    </aside>

    <!-- 主内容区 -->
    <main class="content">
        <div class="operation-bar">
            <div class="title">视图操作能力</div>
            <div class="actions">
                <button class="btn btn-secondary"><i class="fas fa-history"></i> 操作历史</button>
                <button class="btn btn-secondary"><i class="fas fa-cog"></i> 操作设置</button>
                <button class="btn btn-primary"><i class="fas fa-plus"></i> 新建操作</button>
            </div>
        </div>

        <div class="operation-cards">
            <div class="operation-card">
                <div class="card-icon">
                    <i class="fas fa-eye"></i>
                </div>
                <div class="card-title">视图查看</div>
                <div class="card-desc">查看各类运营视图，支持多种视图模式切换，提供数据可视化展示。</div>
            </div>

            <div class="operation-card">
                <div class="card-icon">
                    <i class="fas fa-edit"></i>
                </div>
                <div class="card-title">视图编辑</div>
                <div class="card-desc">编辑现有视图，修改布局、组件和数据配置，实时预览效果。</div>
            </div>

            <div class="operation-card">
                <div class="card-icon">
                    <i class="fas fa-share-alt"></i>
                </div>
                <div class="card-title">视图共享</div>
                <div class="card-desc">将视图分享给其他用户或团队，设置查看和编辑权限。</div>
            </div>

            <div class="operation-card">
                <div class="card-icon">
                    <i class="fas fa-download"></i>
                </div>
                <div class="card-title">导出视图</div>
                <div class="card-desc">将视图导出为图片或PDF格式，用于汇报和分享。</div>
            </div>

            <div class="operation-card">
                <div class="card-icon">
                    <i class="fas fa-clone"></i>
                </div>
                <div class="card-title">视图复制</div>
                <div class="card-desc">复制现有视图作为新视图的基础，快速创建相似视图。</div>
            </div>

            <div class="operation-card">
                <div class="card-icon">
                    <i class="fas fa-trash-alt"></i>
                </div>
                <div class="card-title">视图删除</div>
                <div class="card-desc">删除不再需要的视图，支持批量删除和回收站功能。</div>
            </div>
        </div>

        <div class="operation-logs">
            <div class="logs-header">
                <h3>最近操作日志</h3>
                <div class="logs-filter">
                    <select class="btn btn-secondary">
                        <option value="all">全部类型</option>
                        <option value="view">查看</option>
                        <option value="edit">编辑</option>
                        <option value="delete">删除</option>
                        <option value="share">共享</option>
                    </select>
                    <input type="date" class="btn btn-secondary">
                </div>
            </div>
            <table class="log-table">
                <thead>
                    <tr>
                        <th>操作时间</th>
                        <th>操作用户</th>
                        <th>操作类型</th>
                        <th>操作对象</th>
                        <th>操作详情</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>2023-10-25 14:30:22</td>
                        <td>管理员</td>
                        <td><span class="log-type type-view">查看</span></td>
                        <td>销售数据大屏</td>
                        <td>查看销售数据大屏视图</td>
                        <td>成功</td>
                    </tr>
                    <tr>
                        <td>2023-10-25 13:45:16</td>
                        <td>张经理</td>
                        <td><span class="log-type type-edit">编辑</span></td>
                        <td>运营监控画布</td>
                        <td>修改图表布局和数据字段</td>
                        <td>成功</td>
                    </tr>
                    <tr>
                        <td>2023-10-25 11:20:45</td>
                        <td>李主管</td>
                        <td><span class="log-type type-share">共享</span></td>
                        <td>客户分析报表</td>
                        <td>共享给市场部团队</td>
                        <td>成功</td>
                    </tr>
                    <tr>
                        <td>2023-10-24 16:10:33</td>
                        <td>王专员</td>
                        <td><span class="log-type type-delete">删除</span></td>
                        <td>旧版销售报表</td>
                        <td>删除过时报表</td>
                        <td>成功</td>
                    </tr>
                    <tr>
                        <td>2023-10-24 15:05:18</td>
                        <td>管理员</td>
                        <td><span class="log-type type-view">查看</span></td>
                        <td>系统监控大屏</td>
                        <td>查看系统运行状态</td>
                        <td>成功</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </main>

    <script>
        // 侧边栏菜单交互逻辑
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', function() {
                // 移除所有菜单项的active类
                document.querySelectorAll('.menu-item').forEach(menuItem => {
                    menuItem.classList.remove('active');
                });
                // 为当前点击项添加active类
                this.classList.add('active');
            });
        });

        // 操作卡片点击效果
        document.querySelectorAll('.operation-card').forEach(card => {
            card.addEventListener('click', function() {
                alert('点击了：' + this.querySelector('.card-title').textContent);
            });
        });
    </script>
</body>
</html>