<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自定义报表 - 运营视图</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            color: #333;
        }

        /* 内容区样式 */
        .content {
            margin-left: 240px;
            padding: 20px;
            transition: margin-left 0.3s;
        }

        @media (max-width: 768px) {
            .content {
                margin-left: 0;
            }
        }

        /* 操作栏样式 */
        .operation-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }

        .operation-bar .title {
            font-size: 18px;
            font-weight: 600;
        }

        .operation-bar .actions {
            display: flex;
            gap: 10px;
        }

        /* 报表配置区样式 */
        .report-config {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }

        .config-section {
            margin-bottom: 20px;
        }

        .config-section h3 {
            font-size: 16px;
            margin-bottom: 10px;
            color: #333;
        }

        .config-item {
            margin-bottom: 15px;
        }

        .config-item label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .config-item input,
        .config-item select,
        .config-item textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .config-item textarea {
            height: 100px;
            resize: vertical;
        }

        .config-item .checkbox-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .config-item .checkbox-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        /* 报表预览区样式 */
        .report-preview {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .preview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .preview-content {
            border: 1px solid #eee;
            border-radius: 4px;
            min-height: 300px;
            padding: 15px;
            background-color: #fafafa;
        }

        /* 按钮样式 */
        .btn {
            display: inline-block;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.3s;
        }

        .btn-primary {
            background-color: #1890ff;
            color: white;
        }

        .btn-primary:hover {
            background-color: #40a9ff;
        }

        .btn-secondary {
            background-color: #f5f5f5;
            color: #333;
        }

        .btn-secondary:hover {
            background-color: #e8e8e8;
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="logo-container">
            <img src="img/logo.png" alt="Logo" class="logo">
            <span class="system-name">政企移动项目管理系统</span>
        </div>
        <div class="user-info">
            <span class="user-name">管理员</span>
            <img src="img/user-avatar.png" alt="User Avatar" class="user-avatar">
        </div>
    </header>

    <!-- 侧边栏 -->
    <aside class="sidebar">
        <div class="menu">
            <div class="menu-item">
                <i class="fas fa-home menu-icon"></i>
                <span class="menu-text">首页</span>
            </div>
            <div class="menu-item">
                <i class="fas fa-database menu-icon"></i>
                <span class="menu-text">数据融通</span>
            </div>
            <div class="menu-item">
                <i class="fas fa-brain menu-icon"></i>
                <span class="menu-text">智能洞察分析</span>
            </div>
            <div class="menu-item active">
                <i class="fas fa-chart-bar menu-icon"></i>
                <span class="menu-text">运营视图</span>
                <div style="margin-left: 24px; margin-top: 8px; font-size: 14px;">
                    <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">预置大屏模板</div>
                    <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">预制组件</div>
                    <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">模板管理</div>
                    <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">画布管理</div>
                    <div style="padding: 8px 0; color: var(--primary-color); cursor: pointer;">自定义报表</div>
                    <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">视图操作能力</div>
                </div>
            </div>
            <div class="menu-item">
                <i class="fas fa-desktop menu-icon"></i>
                <span class="menu-text">统一运营门户</span>
            </div>
            <div class="menu-item">
                <i class="fas fa-sitemap menu-icon"></i>
                <span class="menu-text">五级穿透调度</span>
            </div>
            <div class="menu-item">
                <i class="fas fa-microchip menu-icon"></i>
                <span class="menu-text">微服务管理</span>
            </div>
            <div class="menu-item">
                <i class="fas fa-key menu-icon"></i>
                <span class="menu-text">权限管理</span>
            </div>
            <div class="menu-item">
                <i class="fas fa-cog menu-icon"></i>
                <span class="menu-text">系统设置</span>
            </div>
        </div>
    </aside>

    <!-- 主内容区 -->
    <main class="content">
        <div class="operation-bar">
            <div class="title">自定义报表</div>
            <div class="actions">
                <button class="btn btn-secondary"><i class="fas fa-save"></i> 保存配置</button>
                <button class="btn btn-primary"><i class="fas fa-download"></i> 生成报表</button>
            </div>
        </div>

        <div class="report-config">
            <div class="config-section">
                <h3>基本信息</h3>
                <div class="config-item">
                    <label for="report-name">报表名称</label>
                    <input type="text" id="report-name" placeholder="请输入报表名称">
                </div>
                <div class="config-item">
                    <label for="report-desc">报表描述</label>
                    <textarea id="report-desc" placeholder="请输入报表描述"></textarea>
                </div>
                <div class="config-item">
                    <label for="report-type">报表类型</label>
                    <select id="report-type">
                        <option value="daily">日报</option>
                        <option value="weekly">周报</option>
                        <option value="monthly">月报</option>
                        <option value="quarterly">季报</option>
                        <option value="yearly">年报</option>
                        <option value="custom">自定义</option>
                    </select>
                </div>
            </div>

            <div class="config-section">
                <h3>数据来源</h3>
                <div class="config-item">
                    <label for="data-source">数据源</label>
                    <select id="data-source">
                        <option value="sales">销售数据</option>
                        <option value="operation">运营数据</option>
                        <option value="customer">客户数据</option>
                        <option value="product">产品数据</option>
                        <option value="custom">自定义数据源</option>
                    </select>
                </div>
                <div class="config-item">
                    <label for="date-range">日期范围</label>
                    <div style="display: flex; gap: 10px;">
                        <input type="date" id="start-date">
                        <span style="align-self: center;">至</span>
                        <input type="date" id="end-date">
                    </div>
                </div>
            </div>

            <div class="config-section">
                <h3>报表内容</h3>
                <div class="config-item">
                    <label>包含图表</label>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="chart-sales">
                            <label for="chart-sales">销售趋势图</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="chart-revenue">
                            <label for="chart-revenue">收入分析图</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="chart-customer">
                            <label for="chart-customer">客户分布图</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="chart-product">
                            <label for="chart-product">产品占比图</label>
                        </div>
                    </div>
                </div>
                <div class="config-item">
                    <label>包含表格</label>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="table-sales-detail">
                            <label for="table-sales-detail">销售明细表</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="table-customer-ranking">
                            <label for="table-customer-ranking">客户排名表</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="table-product-performance">
                            <label for="table-product-performance">产品表现表</label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="config-section">
                <h3>导出设置</h3>
                <div class="config-item">
                    <label for="export-format">导出格式</label>
                    <select id="export-format">
                        <option value="pdf">PDF</option>
                        <option value="excel">Excel</option>
                        <option value="word">Word</option>
                        <option value="html">HTML</option>
                    </select>
                </div>
                <div class="config-item">
                    <label for="export-orientation">页面方向</label>
                    <select id="export-orientation">
                        <option value="portrait">纵向</option>
                        <option value="landscape">横向</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="report-preview">
            <div class="preview-header">
                <h3>报表预览</h3>
                <button class="btn btn-secondary"><i class="fas fa-refresh"></i> 刷新预览</button>
            </div>
            <div class="preview-content">
                <p style="text-align: center; color: #999; padding-top: 100px;">配置完成后可在此处预览报表效果</p>
            </div>
        </div>
    </main>

    <script>
        // 侧边栏菜单交互逻辑
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', function() {
                // 移除所有菜单项的active类
                document.querySelectorAll('.menu-item').forEach(menuItem => {
                    menuItem.classList.remove('active');
                });
                // 为当前点击项添加active类
                this.classList.add('active');
            });
        });
    </script>
</body>
</html>