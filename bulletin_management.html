<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>数智化运营平台 - 运营通报管理</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <i class="fas fa-chart-line"></i> 数智化运营平台
      </div>
      <div style="display: flex; align-items: center;">
        <div class="dropdown" style="margin-right: 16px;">
          <button class="dropdown-toggle">
            <i class="fas fa-bell"></i>
            <span class="badge">3</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item">
              <div style="font-weight: 500;">新任务通知</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">您有3个新任务需要处理</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">数据采集完成</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">昨日数据采集已完成</div>
            </div>
            <div class="dropdown-item">
              <div style="font-weight: 500;">系统更新</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">平台将于今晚23:00进行维护</div>
            </div>
          </div>
        </div>
        <div class="dropdown">
          <button class="dropdown-toggle">
            <img src="https://picsum.photos/id/1005/40/40" alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 8px;">
            <span>管理员</span>
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-item"><i class="fas fa-user"></i> 个人中心</div>
            <div class="dropdown-item"><i class="fas fa-cog"></i> 系统设置</div>
            <div class="dropdown-item"><i class="fas fa-sign-out-alt"></i> 退出登录</div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- 侧边栏 -->
  <div class="sidebar">
    <div class="menu-item">
      <i class="fas fa-home menu-icon"></i>
      <span class="menu-text">首页</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-database menu-icon"></i>
      <span class="menu-text">数据融通</span>
    </div>
    <div class="menu-item active">
      <i class="fas fa-chart-pie menu-icon"></i>
      <span class="menu-text">智能洞察分析</span>
      <div style="margin-left: 24px; margin-top: 8px; font-size: 14px;">
        <div style="padding: 8px 0; color: var(--text-secondary); cursor: pointer;">运营报告管理</div>
        <div style="padding: 8px 0; color: var(--primary-color); cursor: pointer;">运营通报管理</div>
      </div>
    </div>
    <div class="menu-item">
      <i class="fas fa-chart-bar menu-icon"></i>
      <span class="menu-text">运营视图</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tachometer-alt menu-icon"></i>
      <span class="menu-text">统一运营门户</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-tasks menu-icon"></i>
      <span class="menu-text">五级穿透调度</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-server menu-icon"></i>
      <span class="menu-text">微服务管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-user-shield menu-icon"></i>
      <span class="menu-text">权限管理</span>
    </div>
    <div class="menu-item">
      <i class="fas fa-cog menu-icon"></i>
      <span class="menu-text">系统设置</span>
    </div>
  </div>

  <!-- 主内容区 -->
  <div class="main-content">
    <div class="page-title">
      <i class="fas fa-bullhorn page-title-icon"></i>
      运营通报管理
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <div class="breadcrumb-item"><a href="index.html" style="text-decoration: none; color: inherit;">首页</a></div>
      <div class="breadcrumb-item"><a href="#" style="text-decoration: none; color: inherit;">智能洞察分析</a></div>
      <div class="breadcrumb-item active">运营通报管理</div>
    </div>

    <!-- 标签页 -->
    <div class="tabs">
      <div class="tab active" data-tab-target="bulletinTasks">通报任务管理</div>
      <div class="tab" data-tab-target="bulletinTemplates">通报模板管理</div>
    </div>

    <!-- 通报任务管理 -->
    <div class="tab-content active" id="bulletinTasks">
      <!-- 搜索和操作栏 -->
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
        <div style="display: flex; width: 50%;">
          <div class="search-box" style="width: 300px; margin-bottom: 0; margin-right: 12px;">
            <i class="fas fa-search search-box-icon"></i>
            <input type="text" placeholder="搜索任务...">
          </div>
          <div style="margin-right: 12px;">
            <select style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color);">
              <option value="all">全部状态</option>
              <option value="pending">待审核</option>
              <option value="approved">已审核</option>
              <option value="rejected">已拒绝</option>
              <option value="published">已发布</option>
            </select>
          </div>
        </div>
        <div style="display: flex;">
          <button class="btn btn-primary" data-modal-target="addBulletinTaskModal"><i class="fas fa-plus"></i> 新增通报任务</button>
        </div>
      </div>

      <!-- 通报任务列表 -->
      <div class="card">
        <div class="table-container">
          <table class="table">
            <thead>
              <tr>
                <th>通报标题</th>
                <th>模板名称</th>
                <th>发布人</th>
                <th>状态</th>
                <th>创建时间</th>
                <th>审核时间</th>
                <th>发布时间</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>7月运营数据通报</td>
                <td>月度运营通报模板</td>
                <td>张三</td>
                <td><span class="tag tag-success">已发布</span></td>
                <td>2023-07-15 10:30</td>
                <td>2023-07-15 11:15</td>
                <td>2023-07-15 14:00</td>
                <td>
                  <button class="btn" style="color: var(--primary-color);"><i class="fas fa-eye"></i></button>
                  <button class="btn" style="color: var(--primary-color);" data-modal-target="editBulletinTaskModal"><i class="fas fa-edit"></i></button>
                  <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
                </td>
              </tr>
              <tr>
                <td>活动效果通报</td>
                <td>活动通报模板</td>
                <td>李四</td>
                <td><span class="tag tag-info">已审核</span></td>
                <td>2023-07-16 09:15</td>
                <td>2023-07-16 10:00</td>
                <td>-</td>
                <td>
                  <button class="btn" style="color: var(--primary-color);"><i class="fas fa-eye"></i></button>
                  <button class="btn" style="color: var(--primary-color);" data-modal-target="editBulletinTaskModal"><i class="fas fa-edit"></i></button>
                  <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
                </td>
              </tr>
              <tr>
                <td>用户增长异常通报</td>
                <td>异常通报模板</td>
                <td>王五</td>
                <td><span class="tag tag-warning">待审核</span></td>
                <td>2023-07-16 11:45</td>
                <td>-</td>
                <td>-</td>
                <td>
                  <button class="btn" style="color: var(--primary-color);"><i class="fas fa-eye"></i></button>
                  <button class="btn" style="color: var(--primary-color);" data-modal-target="editBulletinTaskModal"><i class="fas fa-edit"></i></button>
                  <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="pagination">
          <div class="pagination-item"><i class="fas fa-chevron-left"></i></div>
          <div class="pagination-item active">1</div>
          <div class="pagination-item">2</div>
          <div class="pagination-item">3</div>
          <div class="pagination-item"><i class="fas fa-chevron-right"></i></div>
        </div>
      </div>
    </div>

    <!-- 通报模板管理 -->
    <div class="tab-content" id="bulletinTemplates">
      <!-- 搜索和操作栏 -->
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
        <div class="search-box" style="width: 300px; margin-bottom: 0;">
          <i class="fas fa-search search-box-icon"></i>
          <input type="text" placeholder="搜索模板...">
        </div>
        <div style="display: flex;">
          <button class="btn btn-primary" data-modal-target="addBulletinTemplateModal"><i class="fas fa-plus"></i> 新增通报模板</button>
        </div>
      </div>

      <!-- 通报模板列表 -->
      <div class="card">
        <div class="table-container">
          <table class="table">
            <thead>
              <tr>
                <th>模板名称</th>
                <th>模板类型</th>
                <th>创建人</th>
                <th>创建时间</th>
                <th>更新时间</th>
                <th>状态</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>月度运营通报模板</td>
                <td>月度</td>
                <td>管理员</td>
                <td>2023-06-01 14:30</td>
                <td>2023-07-01 10:15</td>
                <td><span class="tag tag-success">启用</span></td>
                <td>
                  <button class="btn" style="color: var(--primary-color);"><i class="fas fa-eye"></i></button>
                  <button class="btn" style="color: var(--primary-color);" data-modal-target="editBulletinTemplateModal"><i class="fas fa-edit"></i></button>
                  <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
                </td>
              </tr>
              <tr>
                <td>活动通报模板</td>
                <td>活动</td>
                <td>张三</td>
                <td>2023-07-05 09:15</td>
                <td>2023-07-10 15:45</td>
                <td><span class="tag tag-success">启用</span></td>
                <td>
                  <button class="btn" style="color: var(--primary-color);"><i class="fas fa-eye"></i></button>
                  <button class="btn" style="color: var(--primary-color);" data-modal-target="editBulletinTemplateModal"><i class="fas fa-edit"></i></button>
                  <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
                </td>
              </tr>
              <tr>
                <td>异常通报模板</td>
                <td>异常</td>
                <td>李四</td>
                <td>2023-07-10 11:20</td>
                <td>2023-07-10 11:20</td>
                <td><span class="tag tag-success">启用</span></td>
                <td>
                  <button class="btn" style="color: var(--primary-color);"><i class="fas fa-eye"></i></button>
                  <button class="btn" style="color: var(--primary-color);" data-modal-target="editBulletinTemplateModal"><i class="fas fa-edit"></i></button>
                  <button class="btn" style="color: var(--danger-color);"><i class="fas fa-trash"></i></button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="pagination">
          <div class="pagination-item"><i class="fas fa-chevron-left"></i></div>
          <div class="pagination-item active">1</div>
          <div class="pagination-item">2</div>
          <div class="pagination-item"><i class="fas fa-chevron-right"></i></div>
        </div>
      </div>
    </div>
  </div>

  <!-- 新增通报任务模态框 -->
  <div class="modal" id="addBulletinTaskModal">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title"><i class="fas fa-plus"></i> 新增通报任务</div>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <form id="addBulletinTaskForm">
          <div class="form-group">
            <label for="bulletinTitle">通报标题</label>
            <input type="text" id="bulletinTitle" name="bulletinTitle" required placeholder="请输入通报标题">
          </div>
          <div class="form-group">
            <label for="bulletinTemplate">通报模板</label>
            <select id="bulletinTemplate" name="bulletinTemplate" required>
              <option value="">请选择通报模板</option>
              <option value="monthly_bulletin">月度运营通报模板</option>
              <option value="activity_bulletin">活动通报模板</option>
              <option value="abnormal_bulletin">异常通报模板</option>
            </select>
          </div>
          <div class="form-group">
            <label for="bulletinContent">通报内容</label>
            <textarea id="bulletinContent" name="bulletinContent" rows="5" required placeholder="请输入通报内容"></textarea>
          </div>
          <div class="form-group">
            <label for="bulletinRecipients">接收人</label>
            <div style="border: 1px solid var(--border-color); border-radius: 4px; padding: 8px;">
              <div style="display: flex; flex-wrap: wrap; gap: 8px;">
                <div class="tag" style="background-color: var(--primary-light); padding: 4px 8px; border-radius: 4px; display: flex; align-items: center;">
                  张三 <i class="fas fa-times" style="margin-left: 4px; cursor: pointer;"></i>
                </div>
                <div class="tag" style="background-color: var(--primary-light); padding: 4px 8px; border-radius: 4px; display: flex; align-items: center;">
                  李四 <i class="fas fa-times" style="margin-left: 4px; cursor: pointer;"></i>
                </div>
                <div style="flex-grow: 1;">
                  <input type="text" placeholder="添加接收人..." style="border: none; outline: none; width: 100%; background: transparent;">
                </div>
              </div>
            </div>
          </div>
          <div class="form-group">
            <label for="bulletinDescription">备注说明</label>
            <textarea id="bulletinDescription" name="bulletinDescription" rows="3" placeholder="请输入备注说明"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button class="btn" style="border: 1px solid var(--border-color);" onclick="document.getElementById('addBulletinTaskModal').classList.remove('show')">取消</button>
        <button class="btn btn-primary" onclick="document.getElementById('addBulletinTaskForm').submit()">提交审核</button>
      </div>
    </div>
  </div>

  <!-- 其他模态框（编辑通报任务、新增通报模板、编辑通报模板）省略 -->

  <script src="js/common.js"></script>
  <script>
    // 标签页切换
    document.querySelectorAll('.tab').forEach(tab => {
      tab.addEventListener('click', () => {
        // 移除所有标签页的active类
        document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
        document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

        // 给当前点击的标签页添加active类
        tab.classList.add('active');

        // 显示对应的内容
        const target = tab.getAttribute('data-tab-target');
        document.getElementById(target).classList.add('active');
      });
    });

    // 新增通报任务表单提交
    document.getElementById('addBulletinTaskForm').addEventListener('submit', function(e) {
      e.preventDefault();
      if (validateForm('addBulletinTaskForm')) {
        // 模拟提交成功
        alert('通报任务已成功提交审核！');
        document.getElementById('addBulletinTaskModal').classList.remove('show');
        // 重置表单
        this.reset();
      }
    });
  </script>
</body>
</html>